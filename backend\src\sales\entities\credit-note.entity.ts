import { <PERSON>tity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Customer } from './customer.entity';
import { Invoice } from './invoice.entity';
import { CreditNoteItem } from './credit-note-item.entity';

@Entity('credit_notes')
export class CreditNote {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  creditNoteNumber: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({ nullable: true })
  invoiceId: string;

  @ManyToOne(() => Invoice, invoice => invoice.creditNotes, { nullable: true })
  @JoinColumn({ name: 'invoiceId' })
  invoice: Invoice;

  @Column({ type: 'date' })
  creditNoteDate: Date;

  @Column()
  reason: string;

  @Column({ type: 'varchar', default: 'return' })
  type: 'return' | 'discount' | 'error' | 'other';

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ type: 'varchar', default: 'draft' })
  status: 'draft' | 'issued' | 'applied' | 'cancelled';

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  appliedToInvoice: string; // Invoice ID where credit was applied

  @OneToMany(() => CreditNoteItem, item => item.creditNote, { cascade: true })
  items: CreditNoteItem[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  tenantId: string;
}
