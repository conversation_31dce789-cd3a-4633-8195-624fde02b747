import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { AuditReport } from './audit-report.entity';
import { User } from '../../user/entities/user.entity';

@Entity('audit_findings')
export class AuditFinding {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'audit_report_id' })
  auditReportId: string;

  @Column()
  category: string;

  @Column()
  subcategory: string;

  @Column({
    type: 'varchar',
    default: 'Medium',
  })
  severity: string;

  @Column()
  title: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text' })
  criteria: string;

  @Column({ type: 'text' })
  condition: string;

  @Column({ type: 'text' })
  cause: string;

  @Column({ type: 'text' })
  effect: string;

  @Column({ type: 'text' })
  recommendation: string;

  @Column({ type: 'text', nullable: true })
  managementResponse: string;

  @Column({ type: 'text', nullable: true })
  correctiveAction: string;

  @Column({
    type: 'varchar',
    default: 'Open',
  })
  status: string;

  @Column()
  department: string;

  @Column()
  processArea: string;

  @Column()
  assignedTo: string;

  @Column({ type: 'date' })
  dueDate: Date;

  @Column({ type: 'date', nullable: true })
  resolvedDate: Date;

  @Column({ type: 'date', nullable: true })
  verifiedDate: Date;

  @Column({ type: 'json', nullable: true })
  riskRating: {
    likelihood: string;
    impact: string;
    overallRisk: string;
  };

  @Column({ type: 'json', nullable: true })
  evidence: {
    type: string;
    description: string;
    reference: string;
    attachments?: string[];
  }[];

  @Column({ type: 'json', nullable: true })
  rootCauseAnalysis: {
    method: string;
    primaryCause: string;
    contributingFactors: string[];
    systemicIssues: string[];
  };

  @Column({ type: 'json', nullable: true })
  remediation: {
    immediateActions: string[];
    longTermActions: string[];
    preventiveControls: string[];
    monitoringPlan: string;
  };

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  financialImpact: number;

  @Column({ type: 'json', nullable: true })
  complianceReferences: {
    standard: string;
    section: string;
    requirement: string;
  }[];

  @Column({ type: 'int', default: 0 })
  recurrenceCount: number;

  @Column({ type: 'date', nullable: true })
  lastOccurrence: Date;

  @Column({ type: 'json', nullable: true })
  relatedFindings: {
    findingId: string;
    relationship: string;
    description: string;
  }[];

  @Column({ type: 'json', nullable: true })
  followUpActions: {
    action: string;
    responsible: string;
    dueDate: Date;
    status: string;
    completedDate?: Date;
  }[];

  @Column({ type: 'text', nullable: true })
  auditorsNotes: string;

  @Column({ type: 'text', nullable: true })
  managementNotes: string;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  // Relations
  @ManyToOne(() => AuditReport, (report) => report.findings)
  @JoinColumn({ name: 'audit_report_id' })
  auditReport: AuditReport;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'assigned_to' })
  assignee: User;
}
