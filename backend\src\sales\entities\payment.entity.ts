import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { Customer } from './customer.entity';
import { Invoice } from './invoice.entity';

@Entity('payments')
export class Payment {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  paymentNumber: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer.payments)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({ nullable: true })
  invoiceId: string;

  @ManyToOne(() => Invoice, invoice => invoice.payments, { nullable: true })
  @JoinColumn({ name: 'invoiceId' })
  invoice: Invoice;

  @Column({ type: 'date' })
  paymentDate: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'varchar', default: 'cash' })
  paymentMethod: 'cash' | 'bank_transfer' | 'credit_card' | 'check' | 'other';

  @Column({ nullable: true })
  referenceNumber: string;

  @Column({ nullable: true })
  bankAccount: string;

  @Column({ type: 'varchar', default: 'received' })
  status: 'received' | 'pending' | 'failed' | 'refunded';

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  receiptNumber: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  exchangeRate: number;

  @Column({ nullable: true })
  currency: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  tenantId: string;
}
