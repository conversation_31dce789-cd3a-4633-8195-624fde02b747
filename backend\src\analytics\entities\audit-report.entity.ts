import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import { Company } from '../../company/entities/company.entity';
import { User } from '../../user/entities/user.entity';
import { AuditFinding } from './audit-finding.entity';

@Entity('audit_reports')
export class AuditReport {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'company_id' })
  companyId: string;

  @Column()
  year: number;

  @Column({ nullable: true })
  quarter: number;

  @Column({
    type: 'varchar',
    default: 'Annual',
  })
  reportType: string;

  @Column({
    type: 'varchar',
    default: 'Draft',
  })
  status: string;

  @Column()
  department: string;

  @Column({
    type: 'varchar',
    default: 'Single Department',
  })
  scope: string;

  @Column({
    type: 'varchar',
    default: 'General',
  })
  auditCategory: string;

  @Column({ type: 'simple-array', nullable: true })
  regulatoryFramework: string[];

  @Column({
    type: 'varchar',
    default: 'Medium',
  })
  riskLevel: string;

  @Column()
  auditor: string;

  @Column({ type: 'text', nullable: true })
  auditScope: string;

  @Column({ type: 'text', nullable: true })
  objectives: string;

  @Column({ type: 'text', nullable: true })
  methodology: string;

  @Column({ type: 'text', nullable: true })
  executiveSummary: string;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0 })
  complianceScore: number;

  @Column({ type: 'int', default: 0 })
  totalFindings: number;

  @Column({ type: 'int', default: 0 })
  criticalFindings: number;

  @Column({ type: 'int', default: 0 })
  highFindings: number;

  @Column({ type: 'int', default: 0 })
  mediumFindings: number;

  @Column({ type: 'int', default: 0 })
  lowFindings: number;

  @Column({ type: 'int', default: 0 })
  recommendations: number;

  @Column({ type: 'date', nullable: true })
  plannedStartDate: Date;

  @Column({ type: 'date', nullable: true })
  plannedEndDate: Date;

  @Column({ type: 'date', nullable: true })
  actualStartDate: Date;

  @Column({ type: 'date', nullable: true })
  actualEndDate: Date;

  @Column({ type: 'date', nullable: true })
  completedDate: Date;

  @Column({ type: 'json', nullable: true })
  auditTeam: {
    leadAuditor: string;
    members: string[];
    externalAuditors?: string[];
  };

  @Column({ type: 'json', nullable: true })
  auditCriteria: {
    standards: string[];
    regulations: string[];
    policies: string[];
  };

  @Column({ type: 'json', nullable: true })
  riskAssessment: {
    inherentRisk: string;
    controlRisk: string;
    detectionRisk: string;
    overallRisk: string;
  };

  @Column({ type: 'json', nullable: true })
  samplingMethod: {
    type: string;
    size: number;
    criteria: string;
  };

  @Column({ type: 'text', nullable: true })
  limitations: string;

  @Column({ type: 'text', nullable: true })
  conclusion: string;

  @Column({ type: 'text', nullable: true })
  managementResponse: string;

  @Column({ type: 'json', nullable: true })
  attachments: {
    fileName: string;
    fileUrl: string;
    fileType: string;
    uploadDate: Date;
  }[];

  @Column({ type: 'json', nullable: true })
  distributionList: {
    name: string;
    email: string;
    role: string;
  }[];

  @Column({ type: 'date', nullable: true })
  nextAuditDate: Date;

  @Column({ type: 'json', nullable: true })
  followUpActions: {
    action: string;
    responsible: string;
    dueDate: Date;
    status: string;
  }[];

  @Column({ type: 'json', nullable: true })
  financialData: {
    totalIncome: number;
    totalExpenses: number;
    netResult: number;
    auditedTransactions: number;
    discrepancies: number;
    materialMisstatements: number;
    departmentBreakdown: {
      department: string;
      income: number;
      expenses: number;
      accuracy: number;
    }[];
    incomeVerification: {
      revenueRecognitionCompliant: boolean;
      invoiceMatchingVerified: boolean;
      cashReceiptsReconciled: boolean;
      timingDifferences: number;
    };
    expenseVerification: {
      purchaseOrderMatching: boolean;
      payrollCalculationsVerified: boolean;
      accrualProceduresCompliant: boolean;
      unauthorizedExpenses: number;
    };
  };

  @Column({ type: 'json', nullable: true })
  specializedData: {
    bsaAmlData?: {
      suspiciousActivityReports: number;
      currencyTransactionReports: number;
      customerDueDiligenceReviews: number;
      sanctionsScreeningResults: number;
      complianceViolations: number;
      sarFilingTimeliness: number;
      ctrAccuracy: number;
      kycCompleteness: number;
    };
    creditData?: {
      loansReviewed: number;
      creditRiskRating: string;
      portfolioQuality: number;
      allowanceAdequacy: number;
      underwritingCompliance: number;
      concentrationRisk: number;
      creditPolicyCompliance: number;
      loanDocumentationScore: number;
    };
    itSecurityData?: {
      vulnerabilitiesFound: number;
      securityIncidents: number;
      complianceGaps: number;
      penetrationTestResults: string;
      dataBreachRisk: string;
      accessControlsScore: number;
      encryptionCompliance: number;
      backupRecoveryScore: number;
    };
    operationsData?: {
      processEfficiency: number;
      controlDeficiencies: number;
      procedureCompliance: number;
      staffingAdequacy: number;
      branchOperationsScore: number;
      cashManagementScore: number;
      customerServiceScore: number;
    };
    trustData?: {
      fiduciaryCompliance: number;
      investmentPolicyAdherence: number;
      clientReportingTimeliness: number;
      regulatoryFilingCompleteness: number;
      assetSafeguarding: number;
      conflictOfInterestManagement: number;
    };
    soxData?: {
      internalControlsEffectiveness: number;
      financialReportingAccuracy: number;
      managementAssessmentScore: number;
      auditCommitteeOversight: number;
      disclosureControlsScore: number;
      fdiciaCertificationStatus: string;
    };
    almData?: {
      interestRateRiskScore: number;
      liquidityRiskScore: number;
      capitalAdequacyRatio: number;
      assetQualityScore: number;
      earningsStability: number;
      managementQuality: number;
    };
  };

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  @CreateDateColumn({ name: 'created_date' })
  createdDate: Date;

  @UpdateDateColumn({ name: 'updated_date' })
  updatedDate: Date;

  // Relations
  @ManyToOne(() => Company)
  @JoinColumn({ name: 'company_id' })
  company: Company;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'updated_by' })
  updater: User;

  @OneToMany(() => AuditFinding, (finding) => finding.auditReport)
  findings: AuditFinding[];
}
