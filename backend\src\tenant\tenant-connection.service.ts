import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DataSource, DataSourceOptions } from 'typeorm';
import { getTenantDatabaseConfig } from '../config/database.config';

@Injectable()
export class TenantConnectionService {
  private readonly logger = new Logger(TenantConnectionService.name);
  private connections: Map<string, DataSource> = new Map();

  constructor(private configService: ConfigService) {}

  async getTenantConnection(tenantId: string): Promise<DataSource> {
    if (this.connections.has(tenantId)) {
      const connection = this.connections.get(tenantId);
      if (connection && connection.isInitialized) {
        return connection;
      }
    }

    const config = getTenantDatabaseConfig(this.configService, tenantId);
    const dataSource = new DataSource(config as DataSourceOptions);

    try {
      await dataSource.initialize();
      this.connections.set(tenantId, dataSource);
      this.logger.log(`Connected to tenant database: ${tenantId}`);
      return dataSource;
    } catch (error) {
      this.logger.error(`Failed to connect to tenant database: ${tenantId}`, error);
      throw error;
    }
  }

  async createTenantDatabase(tenantId: string): Promise<void> {
    const usePostgres = this.configService.get('USE_POSTGRES', 'false') === 'true';

    if (!usePostgres) {
      // For SQLite, just create the connection and synchronize
      try {
        const tenantConnection = await this.getTenantConnection(tenantId);
        await tenantConnection.synchronize();
        this.logger.log(`Created and synchronized SQLite tenant database: ${tenantId}`);
        return;
      } catch (error) {
        this.logger.error(`Failed to create SQLite tenant database: ${tenantId}`, error);
        throw error;
      }
    }

    // PostgreSQL database creation logic
    const masterConfig = {
      type: 'postgres' as const,
      host: this.configService.get('DB_HOST'),
      port: this.configService.get('DB_PORT'),
      username: this.configService.get('DB_USERNAME'),
      password: this.configService.get('DB_PASSWORD'),
      database: 'postgres', // Connect to default postgres database
    };

    const masterDataSource = new DataSource(masterConfig);

    try {
      await masterDataSource.initialize();

      const databaseName = `${this.configService.get('TENANT_DB_PREFIX')}${tenantId}`;

      // Check if database already exists
      const result = await masterDataSource.query(
        'SELECT 1 FROM pg_database WHERE datname = $1',
        [databaseName]
      );

      if (result.length === 0) {
        // Create the database
        await masterDataSource.query(`CREATE DATABASE "${databaseName}"`);
        this.logger.log(`Created tenant database: ${databaseName}`);
      } else {
        this.logger.log(`Tenant database already exists: ${databaseName}`);
      }

      await masterDataSource.destroy();

      // Now connect to the new tenant database and run migrations
      const tenantConnection = await this.getTenantConnection(tenantId);
      await tenantConnection.synchronize();
      this.logger.log(`Synchronized tenant database schema: ${databaseName}`);

    } catch (error) {
      this.logger.error(`Failed to create tenant database: ${tenantId}`, error);
      if (masterDataSource.isInitialized) {
        await masterDataSource.destroy();
      }
      throw error;
    }
  }

  async closeTenantConnection(tenantId: string): Promise<void> {
    const connection = this.connections.get(tenantId);
    if (connection && connection.isInitialized) {
      await connection.destroy();
      this.connections.delete(tenantId);
      this.logger.log(`Closed tenant database connection: ${tenantId}`);
    }
  }

  async closeAllConnections(): Promise<void> {
    const promises = Array.from(this.connections.keys()).map(tenantId =>
      this.closeTenantConnection(tenantId)
    );
    await Promise.all(promises);
  }
}
