import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Invoice } from './invoice.entity';
import { Quotation } from './quotation.entity';
import { Payment } from './payment.entity';

@Entity('customers')
export class Customer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  customerNumber: string;

  @Column()
  name: string;

  @Column({ type: 'varchar' })
  type: 'individual' | 'commercial';

  @Column({ nullable: true })
  email: string;

  @Column({ nullable: true })
  phone: string;

  @Column({ nullable: true })
  address: string;

  @Column({ nullable: true })
  city: string;

  @Column({ nullable: true })
  state: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  postalCode: string;

  @Column({ nullable: true })
  taxNumber: string;

  @Column({ nullable: true })
  commercialRegister: string;

  @Column({ type: 'varchar', default: 'email' })
  billingMethod: 'print' | 'email';

  @Column({ type: 'varchar', default: 'english' })
  displayLanguage: 'english' | 'arabic';

  @Column({ type: 'varchar', default: 'active' })
  status: 'active' | 'inactive';

  @Column({ nullable: true })
  category: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ nullable: true })
  currency: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  creditLimit: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  currentBalance: number;

  @OneToMany(() => Invoice, invoice => invoice.customer)
  invoices: Invoice[];

  @OneToMany(() => Quotation, quotation => quotation.customer)
  quotations: Quotation[];

  @OneToMany(() => Payment, payment => payment.customer)
  payments: Payment[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  tenantId: string;
}
