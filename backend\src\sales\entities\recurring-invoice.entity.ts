import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { Customer } from './customer.entity';
import { RecurringInvoiceItem } from './recurring-invoice-item.entity';

@Entity('recurring_invoices')
export class RecurringInvoice {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  templateName: string;

  @Column()
  customerId: string;

  @ManyToOne(() => Customer, customer => customer)
  @JoinColumn({ name: 'customerId' })
  customer: Customer;

  @Column({ type: 'varchar', default: 'monthly' })
  frequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';

  @Column({ type: 'date' })
  startDate: Date;

  @Column({ type: 'date', nullable: true })
  endDate: Date;

  @Column({ type: 'date' })
  nextInvoiceDate: Date;

  @Column({ type: 'date', nullable: true })
  lastInvoiceDate: Date;

  @Column({ type: 'int', default: 0 })
  invoicesGenerated: number;

  @Column({ type: 'int', nullable: true })
  maxInvoices: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  subtotal: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount: number;

  @Column({ type: 'varchar', default: 'percentage' })
  discountType: 'percentage' | 'amount';

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountValue: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalAmount: number;

  @Column({ type: 'varchar', default: 'active' })
  status: 'active' | 'paused' | 'completed' | 'cancelled';

  @Column({ nullable: true })
  paymentTerms: string;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ type: 'text', nullable: true })
  terms: string;

  @OneToMany(() => RecurringInvoiceItem, item => item.recurringInvoice, { cascade: true })
  items: RecurringInvoiceItem[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ nullable: true })
  tenantId: string;
}
