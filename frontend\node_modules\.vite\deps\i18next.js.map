{"version": 3, "sources": ["../../i18next/dist/esm/i18next.js"], "sourcesContent": ["const isString = obj => typeof obj === 'string';\nconst defer = () => {\n  let res;\n  let rej;\n  const promise = new Promise((resolve, reject) => {\n    res = resolve;\n    rej = reject;\n  });\n  promise.resolve = res;\n  promise.reject = rej;\n  return promise;\n};\nconst makeString = object => {\n  if (object == null) return '';\n  return '' + object;\n};\nconst copy = (a, s, t) => {\n  a.forEach(m => {\n    if (s[m]) t[m] = s[m];\n  });\n};\nconst lastOfPathSeparatorRegExp = /###/g;\nconst cleanKey = key => key && key.indexOf('###') > -1 ? key.replace(lastOfPathSeparatorRegExp, '.') : key;\nconst canNotTraverseDeeper = object => !object || isString(object);\nconst getLastOfPath = (object, path, Empty) => {\n  const stack = !isString(path) ? path : path.split('.');\n  let stackIndex = 0;\n  while (stackIndex < stack.length - 1) {\n    if (canNotTraverseDeeper(object)) return {};\n    const key = cleanKey(stack[stackIndex]);\n    if (!object[key] && Empty) object[key] = new Empty();\n    if (Object.prototype.hasOwnProperty.call(object, key)) {\n      object = object[key];\n    } else {\n      object = {};\n    }\n    ++stackIndex;\n  }\n  if (canNotTraverseDeeper(object)) return {};\n  return {\n    obj: object,\n    k: cleanKey(stack[stackIndex])\n  };\n};\nconst setPath = (object, path, newValue) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  if (obj !== undefined || path.length === 1) {\n    obj[k] = newValue;\n    return;\n  }\n  let e = path[path.length - 1];\n  let p = path.slice(0, path.length - 1);\n  let last = getLastOfPath(object, p, Object);\n  while (last.obj === undefined && p.length) {\n    e = `${p[p.length - 1]}.${e}`;\n    p = p.slice(0, p.length - 1);\n    last = getLastOfPath(object, p, Object);\n    if (last?.obj && typeof last.obj[`${last.k}.${e}`] !== 'undefined') {\n      last.obj = undefined;\n    }\n  }\n  last.obj[`${last.k}.${e}`] = newValue;\n};\nconst pushPath = (object, path, newValue, concat) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path, Object);\n  obj[k] = obj[k] || [];\n  obj[k].push(newValue);\n};\nconst getPath = (object, path) => {\n  const {\n    obj,\n    k\n  } = getLastOfPath(object, path);\n  if (!obj) return undefined;\n  if (!Object.prototype.hasOwnProperty.call(obj, k)) return undefined;\n  return obj[k];\n};\nconst getPathWithDefaults = (data, defaultData, key) => {\n  const value = getPath(data, key);\n  if (value !== undefined) {\n    return value;\n  }\n  return getPath(defaultData, key);\n};\nconst deepExtend = (target, source, overwrite) => {\n  for (const prop in source) {\n    if (prop !== '__proto__' && prop !== 'constructor') {\n      if (prop in target) {\n        if (isString(target[prop]) || target[prop] instanceof String || isString(source[prop]) || source[prop] instanceof String) {\n          if (overwrite) target[prop] = source[prop];\n        } else {\n          deepExtend(target[prop], source[prop], overwrite);\n        }\n      } else {\n        target[prop] = source[prop];\n      }\n    }\n  }\n  return target;\n};\nconst regexEscape = str => str.replace(/[\\-\\[\\]\\/\\{\\}\\(\\)\\*\\+\\?\\.\\\\\\^\\$\\|]/g, '\\\\$&');\nvar _entityMap = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;',\n  \"'\": '&#39;',\n  '/': '&#x2F;'\n};\nconst escape = data => {\n  if (isString(data)) {\n    return data.replace(/[&<>\"'\\/]/g, s => _entityMap[s]);\n  }\n  return data;\n};\nclass RegExpCache {\n  constructor(capacity) {\n    this.capacity = capacity;\n    this.regExpMap = new Map();\n    this.regExpQueue = [];\n  }\n  getRegExp(pattern) {\n    const regExpFromCache = this.regExpMap.get(pattern);\n    if (regExpFromCache !== undefined) {\n      return regExpFromCache;\n    }\n    const regExpNew = new RegExp(pattern);\n    if (this.regExpQueue.length === this.capacity) {\n      this.regExpMap.delete(this.regExpQueue.shift());\n    }\n    this.regExpMap.set(pattern, regExpNew);\n    this.regExpQueue.push(pattern);\n    return regExpNew;\n  }\n}\nconst chars = [' ', ',', '?', '!', ';'];\nconst looksLikeObjectPathRegExpCache = new RegExpCache(20);\nconst looksLikeObjectPath = (key, nsSeparator, keySeparator) => {\n  nsSeparator = nsSeparator || '';\n  keySeparator = keySeparator || '';\n  const possibleChars = chars.filter(c => nsSeparator.indexOf(c) < 0 && keySeparator.indexOf(c) < 0);\n  if (possibleChars.length === 0) return true;\n  const r = looksLikeObjectPathRegExpCache.getRegExp(`(${possibleChars.map(c => c === '?' ? '\\\\?' : c).join('|')})`);\n  let matched = !r.test(key);\n  if (!matched) {\n    const ki = key.indexOf(keySeparator);\n    if (ki > 0 && !r.test(key.substring(0, ki))) {\n      matched = true;\n    }\n  }\n  return matched;\n};\nconst deepFind = (obj, path, keySeparator = '.') => {\n  if (!obj) return undefined;\n  if (obj[path]) {\n    if (!Object.prototype.hasOwnProperty.call(obj, path)) return undefined;\n    return obj[path];\n  }\n  const tokens = path.split(keySeparator);\n  let current = obj;\n  for (let i = 0; i < tokens.length;) {\n    if (!current || typeof current !== 'object') {\n      return undefined;\n    }\n    let next;\n    let nextPath = '';\n    for (let j = i; j < tokens.length; ++j) {\n      if (j !== i) {\n        nextPath += keySeparator;\n      }\n      nextPath += tokens[j];\n      next = current[nextPath];\n      if (next !== undefined) {\n        if (['string', 'number', 'boolean'].indexOf(typeof next) > -1 && j < tokens.length - 1) {\n          continue;\n        }\n        i += j - i + 1;\n        break;\n      }\n    }\n    current = next;\n  }\n  return current;\n};\nconst getCleanedCode = code => code?.replace('_', '-');\n\nconst consoleLogger = {\n  type: 'logger',\n  log(args) {\n    this.output('log', args);\n  },\n  warn(args) {\n    this.output('warn', args);\n  },\n  error(args) {\n    this.output('error', args);\n  },\n  output(type, args) {\n    console?.[type]?.apply?.(console, args);\n  }\n};\nclass Logger {\n  constructor(concreteLogger, options = {}) {\n    this.init(concreteLogger, options);\n  }\n  init(concreteLogger, options = {}) {\n    this.prefix = options.prefix || 'i18next:';\n    this.logger = concreteLogger || consoleLogger;\n    this.options = options;\n    this.debug = options.debug;\n  }\n  log(...args) {\n    return this.forward(args, 'log', '', true);\n  }\n  warn(...args) {\n    return this.forward(args, 'warn', '', true);\n  }\n  error(...args) {\n    return this.forward(args, 'error', '');\n  }\n  deprecate(...args) {\n    return this.forward(args, 'warn', 'WARNING DEPRECATED: ', true);\n  }\n  forward(args, lvl, prefix, debugOnly) {\n    if (debugOnly && !this.debug) return null;\n    if (isString(args[0])) args[0] = `${prefix}${this.prefix} ${args[0]}`;\n    return this.logger[lvl](args);\n  }\n  create(moduleName) {\n    return new Logger(this.logger, {\n      ...{\n        prefix: `${this.prefix}:${moduleName}:`\n      },\n      ...this.options\n    });\n  }\n  clone(options) {\n    options = options || this.options;\n    options.prefix = options.prefix || this.prefix;\n    return new Logger(this.logger, options);\n  }\n}\nvar baseLogger = new Logger();\n\nclass EventEmitter {\n  constructor() {\n    this.observers = {};\n  }\n  on(events, listener) {\n    events.split(' ').forEach(event => {\n      if (!this.observers[event]) this.observers[event] = new Map();\n      const numListeners = this.observers[event].get(listener) || 0;\n      this.observers[event].set(listener, numListeners + 1);\n    });\n    return this;\n  }\n  off(event, listener) {\n    if (!this.observers[event]) return;\n    if (!listener) {\n      delete this.observers[event];\n      return;\n    }\n    this.observers[event].delete(listener);\n  }\n  emit(event, ...args) {\n    if (this.observers[event]) {\n      const cloned = Array.from(this.observers[event].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer(...args);\n        }\n      });\n    }\n    if (this.observers['*']) {\n      const cloned = Array.from(this.observers['*'].entries());\n      cloned.forEach(([observer, numTimesAdded]) => {\n        for (let i = 0; i < numTimesAdded; i++) {\n          observer.apply(observer, [event, ...args]);\n        }\n      });\n    }\n  }\n}\n\nclass ResourceStore extends EventEmitter {\n  constructor(data, options = {\n    ns: ['translation'],\n    defaultNS: 'translation'\n  }) {\n    super();\n    this.data = data || {};\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    if (this.options.ignoreJSONStructure === undefined) {\n      this.options.ignoreJSONStructure = true;\n    }\n  }\n  addNamespaces(ns) {\n    if (this.options.ns.indexOf(ns) < 0) {\n      this.options.ns.push(ns);\n    }\n  }\n  removeNamespaces(ns) {\n    const index = this.options.ns.indexOf(ns);\n    if (index > -1) {\n      this.options.ns.splice(index, 1);\n    }\n  }\n  getResource(lng, ns, key, options = {}) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    const ignoreJSONStructure = options.ignoreJSONStructure !== undefined ? options.ignoreJSONStructure : this.options.ignoreJSONStructure;\n    let path;\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n    } else {\n      path = [lng, ns];\n      if (key) {\n        if (Array.isArray(key)) {\n          path.push(...key);\n        } else if (isString(key) && keySeparator) {\n          path.push(...key.split(keySeparator));\n        } else {\n          path.push(key);\n        }\n      }\n    }\n    const result = getPath(this.data, path);\n    if (!result && !ns && !key && lng.indexOf('.') > -1) {\n      lng = path[0];\n      ns = path[1];\n      key = path.slice(2).join('.');\n    }\n    if (result || !ignoreJSONStructure || !isString(key)) return result;\n    return deepFind(this.data?.[lng]?.[ns], key, keySeparator);\n  }\n  addResource(lng, ns, key, value, options = {\n    silent: false\n  }) {\n    const keySeparator = options.keySeparator !== undefined ? options.keySeparator : this.options.keySeparator;\n    let path = [lng, ns];\n    if (key) path = path.concat(keySeparator ? key.split(keySeparator) : key);\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      value = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    setPath(this.data, path, value);\n    if (!options.silent) this.emit('added', lng, ns, key, value);\n  }\n  addResources(lng, ns, resources, options = {\n    silent: false\n  }) {\n    for (const m in resources) {\n      if (isString(resources[m]) || Array.isArray(resources[m])) this.addResource(lng, ns, m, resources[m], {\n        silent: true\n      });\n    }\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  addResourceBundle(lng, ns, resources, deep, overwrite, options = {\n    silent: false,\n    skipCopy: false\n  }) {\n    let path = [lng, ns];\n    if (lng.indexOf('.') > -1) {\n      path = lng.split('.');\n      deep = resources;\n      resources = ns;\n      ns = path[1];\n    }\n    this.addNamespaces(ns);\n    let pack = getPath(this.data, path) || {};\n    if (!options.skipCopy) resources = JSON.parse(JSON.stringify(resources));\n    if (deep) {\n      deepExtend(pack, resources, overwrite);\n    } else {\n      pack = {\n        ...pack,\n        ...resources\n      };\n    }\n    setPath(this.data, path, pack);\n    if (!options.silent) this.emit('added', lng, ns, resources);\n  }\n  removeResourceBundle(lng, ns) {\n    if (this.hasResourceBundle(lng, ns)) {\n      delete this.data[lng][ns];\n    }\n    this.removeNamespaces(ns);\n    this.emit('removed', lng, ns);\n  }\n  hasResourceBundle(lng, ns) {\n    return this.getResource(lng, ns) !== undefined;\n  }\n  getResourceBundle(lng, ns) {\n    if (!ns) ns = this.options.defaultNS;\n    return this.getResource(lng, ns);\n  }\n  getDataByLanguage(lng) {\n    return this.data[lng];\n  }\n  hasLanguageSomeTranslations(lng) {\n    const data = this.getDataByLanguage(lng);\n    const n = data && Object.keys(data) || [];\n    return !!n.find(v => data[v] && Object.keys(data[v]).length > 0);\n  }\n  toJSON() {\n    return this.data;\n  }\n}\n\nvar postProcessor = {\n  processors: {},\n  addPostProcessor(module) {\n    this.processors[module.name] = module;\n  },\n  handle(processors, value, key, options, translator) {\n    processors.forEach(processor => {\n      value = this.processors[processor]?.process(value, key, options, translator) ?? value;\n    });\n    return value;\n  }\n};\n\nconst checkedLoadedFor = {};\nconst shouldHandleAsObject = res => !isString(res) && typeof res !== 'boolean' && typeof res !== 'number';\nclass Translator extends EventEmitter {\n  constructor(services, options = {}) {\n    super();\n    copy(['resourceStore', 'languageUtils', 'pluralResolver', 'interpolator', 'backendConnector', 'i18nFormat', 'utils'], services, this);\n    this.options = options;\n    if (this.options.keySeparator === undefined) {\n      this.options.keySeparator = '.';\n    }\n    this.logger = baseLogger.create('translator');\n  }\n  changeLanguage(lng) {\n    if (lng) this.language = lng;\n  }\n  exists(key, o = {\n    interpolation: {}\n  }) {\n    const opt = {\n      ...o\n    };\n    if (key == null) return false;\n    const resolved = this.resolve(key, opt);\n    return resolved?.res !== undefined;\n  }\n  extractFromKey(key, opt) {\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    let namespaces = opt.ns || this.options.defaultNS || [];\n    const wouldCheckForNsInKey = nsSeparator && key.indexOf(nsSeparator) > -1;\n    const seemsNaturalLanguage = !this.options.userDefinedKeySeparator && !opt.keySeparator && !this.options.userDefinedNsSeparator && !opt.nsSeparator && !looksLikeObjectPath(key, nsSeparator, keySeparator);\n    if (wouldCheckForNsInKey && !seemsNaturalLanguage) {\n      const m = key.match(this.interpolator.nestingRegexp);\n      if (m && m.length > 0) {\n        return {\n          key,\n          namespaces: isString(namespaces) ? [namespaces] : namespaces\n        };\n      }\n      const parts = key.split(nsSeparator);\n      if (nsSeparator !== keySeparator || nsSeparator === keySeparator && this.options.ns.indexOf(parts[0]) > -1) namespaces = parts.shift();\n      key = parts.join(keySeparator);\n    }\n    return {\n      key,\n      namespaces: isString(namespaces) ? [namespaces] : namespaces\n    };\n  }\n  translate(keys, o, lastKey) {\n    let opt = typeof o === 'object' ? {\n      ...o\n    } : o;\n    if (typeof opt !== 'object' && this.options.overloadTranslationOptionHandler) {\n      opt = this.options.overloadTranslationOptionHandler(arguments);\n    }\n    if (typeof options === 'object') opt = {\n      ...opt\n    };\n    if (!opt) opt = {};\n    if (keys == null) return '';\n    if (!Array.isArray(keys)) keys = [String(keys)];\n    const returnDetails = opt.returnDetails !== undefined ? opt.returnDetails : this.options.returnDetails;\n    const keySeparator = opt.keySeparator !== undefined ? opt.keySeparator : this.options.keySeparator;\n    const {\n      key,\n      namespaces\n    } = this.extractFromKey(keys[keys.length - 1], opt);\n    const namespace = namespaces[namespaces.length - 1];\n    let nsSeparator = opt.nsSeparator !== undefined ? opt.nsSeparator : this.options.nsSeparator;\n    if (nsSeparator === undefined) nsSeparator = ':';\n    const lng = opt.lng || this.language;\n    const appendNamespaceToCIMode = opt.appendNamespaceToCIMode || this.options.appendNamespaceToCIMode;\n    if (lng?.toLowerCase() === 'cimode') {\n      if (appendNamespaceToCIMode) {\n        if (returnDetails) {\n          return {\n            res: `${namespace}${nsSeparator}${key}`,\n            usedKey: key,\n            exactUsedKey: key,\n            usedLng: lng,\n            usedNS: namespace,\n            usedParams: this.getUsedParamsDetails(opt)\n          };\n        }\n        return `${namespace}${nsSeparator}${key}`;\n      }\n      if (returnDetails) {\n        return {\n          res: key,\n          usedKey: key,\n          exactUsedKey: key,\n          usedLng: lng,\n          usedNS: namespace,\n          usedParams: this.getUsedParamsDetails(opt)\n        };\n      }\n      return key;\n    }\n    const resolved = this.resolve(keys, opt);\n    let res = resolved?.res;\n    const resUsedKey = resolved?.usedKey || key;\n    const resExactUsedKey = resolved?.exactUsedKey || key;\n    const noObject = ['[object Number]', '[object Function]', '[object RegExp]'];\n    const joinArrays = opt.joinArrays !== undefined ? opt.joinArrays : this.options.joinArrays;\n    const handleAsObjectInI18nFormat = !this.i18nFormat || this.i18nFormat.handleAsObject;\n    const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n    const hasDefaultValue = Translator.hasDefaultValue(opt);\n    const defaultValueSuffix = needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, opt) : '';\n    const defaultValueSuffixOrdinalFallback = opt.ordinal && needsPluralHandling ? this.pluralResolver.getSuffix(lng, opt.count, {\n      ordinal: false\n    }) : '';\n    const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n    const defaultValue = needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] || opt[`defaultValue${defaultValueSuffix}`] || opt[`defaultValue${defaultValueSuffixOrdinalFallback}`] || opt.defaultValue;\n    let resForObjHndl = res;\n    if (handleAsObjectInI18nFormat && !res && hasDefaultValue) {\n      resForObjHndl = defaultValue;\n    }\n    const handleAsObject = shouldHandleAsObject(resForObjHndl);\n    const resType = Object.prototype.toString.apply(resForObjHndl);\n    if (handleAsObjectInI18nFormat && resForObjHndl && handleAsObject && noObject.indexOf(resType) < 0 && !(isString(joinArrays) && Array.isArray(resForObjHndl))) {\n      if (!opt.returnObjects && !this.options.returnObjects) {\n        if (!this.options.returnedObjectHandler) {\n          this.logger.warn('accessing an object - but returnObjects options is not enabled!');\n        }\n        const r = this.options.returnedObjectHandler ? this.options.returnedObjectHandler(resUsedKey, resForObjHndl, {\n          ...opt,\n          ns: namespaces\n        }) : `key '${key} (${this.language})' returned an object instead of string.`;\n        if (returnDetails) {\n          resolved.res = r;\n          resolved.usedParams = this.getUsedParamsDetails(opt);\n          return resolved;\n        }\n        return r;\n      }\n      if (keySeparator) {\n        const resTypeIsArray = Array.isArray(resForObjHndl);\n        const copy = resTypeIsArray ? [] : {};\n        const newKeyToUse = resTypeIsArray ? resExactUsedKey : resUsedKey;\n        for (const m in resForObjHndl) {\n          if (Object.prototype.hasOwnProperty.call(resForObjHndl, m)) {\n            const deepKey = `${newKeyToUse}${keySeparator}${m}`;\n            if (hasDefaultValue && !res) {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                defaultValue: shouldHandleAsObject(defaultValue) ? defaultValue[m] : undefined,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            } else {\n              copy[m] = this.translate(deepKey, {\n                ...opt,\n                ...{\n                  joinArrays: false,\n                  ns: namespaces\n                }\n              });\n            }\n            if (copy[m] === deepKey) copy[m] = resForObjHndl[m];\n          }\n        }\n        res = copy;\n      }\n    } else if (handleAsObjectInI18nFormat && isString(joinArrays) && Array.isArray(res)) {\n      res = res.join(joinArrays);\n      if (res) res = this.extendTranslation(res, keys, opt, lastKey);\n    } else {\n      let usedDefault = false;\n      let usedKey = false;\n      if (!this.isValidLookup(res) && hasDefaultValue) {\n        usedDefault = true;\n        res = defaultValue;\n      }\n      if (!this.isValidLookup(res)) {\n        usedKey = true;\n        res = key;\n      }\n      const missingKeyNoValueFallbackToKey = opt.missingKeyNoValueFallbackToKey || this.options.missingKeyNoValueFallbackToKey;\n      const resForMissing = missingKeyNoValueFallbackToKey && usedKey ? undefined : res;\n      const updateMissing = hasDefaultValue && defaultValue !== res && this.options.updateMissing;\n      if (usedKey || usedDefault || updateMissing) {\n        this.logger.log(updateMissing ? 'updateKey' : 'missingKey', lng, namespace, key, updateMissing ? defaultValue : res);\n        if (keySeparator) {\n          const fk = this.resolve(key, {\n            ...opt,\n            keySeparator: false\n          });\n          if (fk && fk.res) this.logger.warn('Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.');\n        }\n        let lngs = [];\n        const fallbackLngs = this.languageUtils.getFallbackCodes(this.options.fallbackLng, opt.lng || this.language);\n        if (this.options.saveMissingTo === 'fallback' && fallbackLngs && fallbackLngs[0]) {\n          for (let i = 0; i < fallbackLngs.length; i++) {\n            lngs.push(fallbackLngs[i]);\n          }\n        } else if (this.options.saveMissingTo === 'all') {\n          lngs = this.languageUtils.toResolveHierarchy(opt.lng || this.language);\n        } else {\n          lngs.push(opt.lng || this.language);\n        }\n        const send = (l, k, specificDefaultValue) => {\n          const defaultForMissing = hasDefaultValue && specificDefaultValue !== res ? specificDefaultValue : resForMissing;\n          if (this.options.missingKeyHandler) {\n            this.options.missingKeyHandler(l, namespace, k, defaultForMissing, updateMissing, opt);\n          } else if (this.backendConnector?.saveMissing) {\n            this.backendConnector.saveMissing(l, namespace, k, defaultForMissing, updateMissing, opt);\n          }\n          this.emit('missingKey', l, namespace, k, res);\n        };\n        if (this.options.saveMissing) {\n          if (this.options.saveMissingPlurals && needsPluralHandling) {\n            lngs.forEach(language => {\n              const suffixes = this.pluralResolver.getSuffixes(language, opt);\n              if (needsZeroSuffixLookup && opt[`defaultValue${this.options.pluralSeparator}zero`] && suffixes.indexOf(`${this.options.pluralSeparator}zero`) < 0) {\n                suffixes.push(`${this.options.pluralSeparator}zero`);\n              }\n              suffixes.forEach(suffix => {\n                send([language], key + suffix, opt[`defaultValue${suffix}`] || defaultValue);\n              });\n            });\n          } else {\n            send(lngs, key, defaultValue);\n          }\n        }\n      }\n      res = this.extendTranslation(res, keys, opt, resolved, lastKey);\n      if (usedKey && res === key && this.options.appendNamespaceToMissingKey) {\n        res = `${namespace}${nsSeparator}${key}`;\n      }\n      if ((usedKey || usedDefault) && this.options.parseMissingKeyHandler) {\n        res = this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey ? `${namespace}${nsSeparator}${key}` : key, usedDefault ? res : undefined, opt);\n      }\n    }\n    if (returnDetails) {\n      resolved.res = res;\n      resolved.usedParams = this.getUsedParamsDetails(opt);\n      return resolved;\n    }\n    return res;\n  }\n  extendTranslation(res, key, opt, resolved, lastKey) {\n    if (this.i18nFormat?.parse) {\n      res = this.i18nFormat.parse(res, {\n        ...this.options.interpolation.defaultVariables,\n        ...opt\n      }, opt.lng || this.language || resolved.usedLng, resolved.usedNS, resolved.usedKey, {\n        resolved\n      });\n    } else if (!opt.skipInterpolation) {\n      if (opt.interpolation) this.interpolator.init({\n        ...opt,\n        ...{\n          interpolation: {\n            ...this.options.interpolation,\n            ...opt.interpolation\n          }\n        }\n      });\n      const skipOnVariables = isString(res) && (opt?.interpolation?.skipOnVariables !== undefined ? opt.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables);\n      let nestBef;\n      if (skipOnVariables) {\n        const nb = res.match(this.interpolator.nestingRegexp);\n        nestBef = nb && nb.length;\n      }\n      let data = opt.replace && !isString(opt.replace) ? opt.replace : opt;\n      if (this.options.interpolation.defaultVariables) data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n      res = this.interpolator.interpolate(res, data, opt.lng || this.language || resolved.usedLng, opt);\n      if (skipOnVariables) {\n        const na = res.match(this.interpolator.nestingRegexp);\n        const nestAft = na && na.length;\n        if (nestBef < nestAft) opt.nest = false;\n      }\n      if (!opt.lng && resolved && resolved.res) opt.lng = this.language || resolved.usedLng;\n      if (opt.nest !== false) res = this.interpolator.nest(res, (...args) => {\n        if (lastKey?.[0] === args[0] && !opt.context) {\n          this.logger.warn(`It seems you are nesting recursively key: ${args[0]} in key: ${key[0]}`);\n          return null;\n        }\n        return this.translate(...args, key);\n      }, opt);\n      if (opt.interpolation) this.interpolator.reset();\n    }\n    const postProcess = opt.postProcess || this.options.postProcess;\n    const postProcessorNames = isString(postProcess) ? [postProcess] : postProcess;\n    if (res != null && postProcessorNames?.length && opt.applyPostProcessor !== false) {\n      res = postProcessor.handle(postProcessorNames, res, key, this.options && this.options.postProcessPassResolved ? {\n        i18nResolved: {\n          ...resolved,\n          usedParams: this.getUsedParamsDetails(opt)\n        },\n        ...opt\n      } : opt, this);\n    }\n    return res;\n  }\n  resolve(keys, opt = {}) {\n    let found;\n    let usedKey;\n    let exactUsedKey;\n    let usedLng;\n    let usedNS;\n    if (isString(keys)) keys = [keys];\n    keys.forEach(k => {\n      if (this.isValidLookup(found)) return;\n      const extracted = this.extractFromKey(k, opt);\n      const key = extracted.key;\n      usedKey = key;\n      let namespaces = extracted.namespaces;\n      if (this.options.fallbackNS) namespaces = namespaces.concat(this.options.fallbackNS);\n      const needsPluralHandling = opt.count !== undefined && !isString(opt.count);\n      const needsZeroSuffixLookup = needsPluralHandling && !opt.ordinal && opt.count === 0;\n      const needsContextHandling = opt.context !== undefined && (isString(opt.context) || typeof opt.context === 'number') && opt.context !== '';\n      const codes = opt.lngs ? opt.lngs : this.languageUtils.toResolveHierarchy(opt.lng || this.language, opt.fallbackLng);\n      namespaces.forEach(ns => {\n        if (this.isValidLookup(found)) return;\n        usedNS = ns;\n        if (!checkedLoadedFor[`${codes[0]}-${ns}`] && this.utils?.hasLoadedNamespace && !this.utils?.hasLoadedNamespace(usedNS)) {\n          checkedLoadedFor[`${codes[0]}-${ns}`] = true;\n          this.logger.warn(`key \"${usedKey}\" for languages \"${codes.join(', ')}\" won't get resolved as namespace \"${usedNS}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n        }\n        codes.forEach(code => {\n          if (this.isValidLookup(found)) return;\n          usedLng = code;\n          const finalKeys = [key];\n          if (this.i18nFormat?.addLookupKeys) {\n            this.i18nFormat.addLookupKeys(finalKeys, key, code, ns, opt);\n          } else {\n            let pluralSuffix;\n            if (needsPluralHandling) pluralSuffix = this.pluralResolver.getSuffix(code, opt.count, opt);\n            const zeroSuffix = `${this.options.pluralSeparator}zero`;\n            const ordinalPrefix = `${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;\n            if (needsPluralHandling) {\n              finalKeys.push(key + pluralSuffix);\n              if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                finalKeys.push(key + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n              }\n              if (needsZeroSuffixLookup) {\n                finalKeys.push(key + zeroSuffix);\n              }\n            }\n            if (needsContextHandling) {\n              const contextKey = `${key}${this.options.contextSeparator}${opt.context}`;\n              finalKeys.push(contextKey);\n              if (needsPluralHandling) {\n                finalKeys.push(contextKey + pluralSuffix);\n                if (opt.ordinal && pluralSuffix.indexOf(ordinalPrefix) === 0) {\n                  finalKeys.push(contextKey + pluralSuffix.replace(ordinalPrefix, this.options.pluralSeparator));\n                }\n                if (needsZeroSuffixLookup) {\n                  finalKeys.push(contextKey + zeroSuffix);\n                }\n              }\n            }\n          }\n          let possibleKey;\n          while (possibleKey = finalKeys.pop()) {\n            if (!this.isValidLookup(found)) {\n              exactUsedKey = possibleKey;\n              found = this.getResource(code, ns, possibleKey, opt);\n            }\n          }\n        });\n      });\n    });\n    return {\n      res: found,\n      usedKey,\n      exactUsedKey,\n      usedLng,\n      usedNS\n    };\n  }\n  isValidLookup(res) {\n    return res !== undefined && !(!this.options.returnNull && res === null) && !(!this.options.returnEmptyString && res === '');\n  }\n  getResource(code, ns, key, options = {}) {\n    if (this.i18nFormat?.getResource) return this.i18nFormat.getResource(code, ns, key, options);\n    return this.resourceStore.getResource(code, ns, key, options);\n  }\n  getUsedParamsDetails(options = {}) {\n    const optionsKeys = ['defaultValue', 'ordinal', 'context', 'replace', 'lng', 'lngs', 'fallbackLng', 'ns', 'keySeparator', 'nsSeparator', 'returnObjects', 'returnDetails', 'joinArrays', 'postProcess', 'interpolation'];\n    const useOptionsReplaceForData = options.replace && !isString(options.replace);\n    let data = useOptionsReplaceForData ? options.replace : options;\n    if (useOptionsReplaceForData && typeof options.count !== 'undefined') {\n      data.count = options.count;\n    }\n    if (this.options.interpolation.defaultVariables) {\n      data = {\n        ...this.options.interpolation.defaultVariables,\n        ...data\n      };\n    }\n    if (!useOptionsReplaceForData) {\n      data = {\n        ...data\n      };\n      for (const key of optionsKeys) {\n        delete data[key];\n      }\n    }\n    return data;\n  }\n  static hasDefaultValue(options) {\n    const prefix = 'defaultValue';\n    for (const option in options) {\n      if (Object.prototype.hasOwnProperty.call(options, option) && prefix === option.substring(0, prefix.length) && undefined !== options[option]) {\n        return true;\n      }\n    }\n    return false;\n  }\n}\n\nclass LanguageUtil {\n  constructor(options) {\n    this.options = options;\n    this.supportedLngs = this.options.supportedLngs || false;\n    this.logger = baseLogger.create('languageUtils');\n  }\n  getScriptPartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return null;\n    const p = code.split('-');\n    if (p.length === 2) return null;\n    p.pop();\n    if (p[p.length - 1].toLowerCase() === 'x') return null;\n    return this.formatLanguageCode(p.join('-'));\n  }\n  getLanguagePartFromCode(code) {\n    code = getCleanedCode(code);\n    if (!code || code.indexOf('-') < 0) return code;\n    const p = code.split('-');\n    return this.formatLanguageCode(p[0]);\n  }\n  formatLanguageCode(code) {\n    if (isString(code) && code.indexOf('-') > -1) {\n      let formattedCode;\n      try {\n        formattedCode = Intl.getCanonicalLocales(code)[0];\n      } catch (e) {}\n      if (formattedCode && this.options.lowerCaseLng) {\n        formattedCode = formattedCode.toLowerCase();\n      }\n      if (formattedCode) return formattedCode;\n      if (this.options.lowerCaseLng) {\n        return code.toLowerCase();\n      }\n      return code;\n    }\n    return this.options.cleanCode || this.options.lowerCaseLng ? code.toLowerCase() : code;\n  }\n  isSupportedCode(code) {\n    if (this.options.load === 'languageOnly' || this.options.nonExplicitSupportedLngs) {\n      code = this.getLanguagePartFromCode(code);\n    }\n    return !this.supportedLngs || !this.supportedLngs.length || this.supportedLngs.indexOf(code) > -1;\n  }\n  getBestMatchFromCodes(codes) {\n    if (!codes) return null;\n    let found;\n    codes.forEach(code => {\n      if (found) return;\n      const cleanedLng = this.formatLanguageCode(code);\n      if (!this.options.supportedLngs || this.isSupportedCode(cleanedLng)) found = cleanedLng;\n    });\n    if (!found && this.options.supportedLngs) {\n      codes.forEach(code => {\n        if (found) return;\n        const lngScOnly = this.getScriptPartFromCode(code);\n        if (this.isSupportedCode(lngScOnly)) return found = lngScOnly;\n        const lngOnly = this.getLanguagePartFromCode(code);\n        if (this.isSupportedCode(lngOnly)) return found = lngOnly;\n        found = this.options.supportedLngs.find(supportedLng => {\n          if (supportedLng === lngOnly) return supportedLng;\n          if (supportedLng.indexOf('-') < 0 && lngOnly.indexOf('-') < 0) return;\n          if (supportedLng.indexOf('-') > 0 && lngOnly.indexOf('-') < 0 && supportedLng.substring(0, supportedLng.indexOf('-')) === lngOnly) return supportedLng;\n          if (supportedLng.indexOf(lngOnly) === 0 && lngOnly.length > 1) return supportedLng;\n        });\n      });\n    }\n    if (!found) found = this.getFallbackCodes(this.options.fallbackLng)[0];\n    return found;\n  }\n  getFallbackCodes(fallbacks, code) {\n    if (!fallbacks) return [];\n    if (typeof fallbacks === 'function') fallbacks = fallbacks(code);\n    if (isString(fallbacks)) fallbacks = [fallbacks];\n    if (Array.isArray(fallbacks)) return fallbacks;\n    if (!code) return fallbacks.default || [];\n    let found = fallbacks[code];\n    if (!found) found = fallbacks[this.getScriptPartFromCode(code)];\n    if (!found) found = fallbacks[this.formatLanguageCode(code)];\n    if (!found) found = fallbacks[this.getLanguagePartFromCode(code)];\n    if (!found) found = fallbacks.default;\n    return found || [];\n  }\n  toResolveHierarchy(code, fallbackCode) {\n    const fallbackCodes = this.getFallbackCodes(fallbackCode || this.options.fallbackLng || [], code);\n    const codes = [];\n    const addCode = c => {\n      if (!c) return;\n      if (this.isSupportedCode(c)) {\n        codes.push(c);\n      } else {\n        this.logger.warn(`rejecting language code not found in supportedLngs: ${c}`);\n      }\n    };\n    if (isString(code) && (code.indexOf('-') > -1 || code.indexOf('_') > -1)) {\n      if (this.options.load !== 'languageOnly') addCode(this.formatLanguageCode(code));\n      if (this.options.load !== 'languageOnly' && this.options.load !== 'currentOnly') addCode(this.getScriptPartFromCode(code));\n      if (this.options.load !== 'currentOnly') addCode(this.getLanguagePartFromCode(code));\n    } else if (isString(code)) {\n      addCode(this.formatLanguageCode(code));\n    }\n    fallbackCodes.forEach(fc => {\n      if (codes.indexOf(fc) < 0) addCode(this.formatLanguageCode(fc));\n    });\n    return codes;\n  }\n}\n\nconst suffixesOrder = {\n  zero: 0,\n  one: 1,\n  two: 2,\n  few: 3,\n  many: 4,\n  other: 5\n};\nconst dummyRule = {\n  select: count => count === 1 ? 'one' : 'other',\n  resolvedOptions: () => ({\n    pluralCategories: ['one', 'other']\n  })\n};\nclass PluralResolver {\n  constructor(languageUtils, options = {}) {\n    this.languageUtils = languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('pluralResolver');\n    this.pluralRulesCache = {};\n  }\n  addRule(lng, obj) {\n    this.rules[lng] = obj;\n  }\n  clearCache() {\n    this.pluralRulesCache = {};\n  }\n  getRule(code, options = {}) {\n    const cleanedCode = getCleanedCode(code === 'dev' ? 'en' : code);\n    const type = options.ordinal ? 'ordinal' : 'cardinal';\n    const cacheKey = JSON.stringify({\n      cleanedCode,\n      type\n    });\n    if (cacheKey in this.pluralRulesCache) {\n      return this.pluralRulesCache[cacheKey];\n    }\n    let rule;\n    try {\n      rule = new Intl.PluralRules(cleanedCode, {\n        type\n      });\n    } catch (err) {\n      if (!Intl) {\n        this.logger.error('No Intl support, please use an Intl polyfill!');\n        return dummyRule;\n      }\n      if (!code.match(/-|_/)) return dummyRule;\n      const lngPart = this.languageUtils.getLanguagePartFromCode(code);\n      rule = this.getRule(lngPart, options);\n    }\n    this.pluralRulesCache[cacheKey] = rule;\n    return rule;\n  }\n  needsPlural(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    return rule?.resolvedOptions().pluralCategories.length > 1;\n  }\n  getPluralFormsOfKey(code, key, options = {}) {\n    return this.getSuffixes(code, options).map(suffix => `${key}${suffix}`);\n  }\n  getSuffixes(code, options = {}) {\n    let rule = this.getRule(code, options);\n    if (!rule) rule = this.getRule('dev', options);\n    if (!rule) return [];\n    return rule.resolvedOptions().pluralCategories.sort((pluralCategory1, pluralCategory2) => suffixesOrder[pluralCategory1] - suffixesOrder[pluralCategory2]).map(pluralCategory => `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${pluralCategory}`);\n  }\n  getSuffix(code, count, options = {}) {\n    const rule = this.getRule(code, options);\n    if (rule) {\n      return `${this.options.prepend}${options.ordinal ? `ordinal${this.options.prepend}` : ''}${rule.select(count)}`;\n    }\n    this.logger.warn(`no plural rule found for: ${code}`);\n    return this.getSuffix('dev', count, options);\n  }\n}\n\nconst deepFindWithDefaults = (data, defaultData, key, keySeparator = '.', ignoreJSONStructure = true) => {\n  let path = getPathWithDefaults(data, defaultData, key);\n  if (!path && ignoreJSONStructure && isString(key)) {\n    path = deepFind(data, key, keySeparator);\n    if (path === undefined) path = deepFind(defaultData, key, keySeparator);\n  }\n  return path;\n};\nconst regexSafe = val => val.replace(/\\$/g, '$$$$');\nclass Interpolator {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('interpolator');\n    this.options = options;\n    this.format = options?.interpolation?.format || (value => value);\n    this.init(options);\n  }\n  init(options = {}) {\n    if (!options.interpolation) options.interpolation = {\n      escapeValue: true\n    };\n    const {\n      escape: escape$1,\n      escapeValue,\n      useRawValueToEscape,\n      prefix,\n      prefixEscaped,\n      suffix,\n      suffixEscaped,\n      formatSeparator,\n      unescapeSuffix,\n      unescapePrefix,\n      nestingPrefix,\n      nestingPrefixEscaped,\n      nestingSuffix,\n      nestingSuffixEscaped,\n      nestingOptionsSeparator,\n      maxReplaces,\n      alwaysFormat\n    } = options.interpolation;\n    this.escape = escape$1 !== undefined ? escape$1 : escape;\n    this.escapeValue = escapeValue !== undefined ? escapeValue : true;\n    this.useRawValueToEscape = useRawValueToEscape !== undefined ? useRawValueToEscape : false;\n    this.prefix = prefix ? regexEscape(prefix) : prefixEscaped || '{{';\n    this.suffix = suffix ? regexEscape(suffix) : suffixEscaped || '}}';\n    this.formatSeparator = formatSeparator || ',';\n    this.unescapePrefix = unescapeSuffix ? '' : unescapePrefix || '-';\n    this.unescapeSuffix = this.unescapePrefix ? '' : unescapeSuffix || '';\n    this.nestingPrefix = nestingPrefix ? regexEscape(nestingPrefix) : nestingPrefixEscaped || regexEscape('$t(');\n    this.nestingSuffix = nestingSuffix ? regexEscape(nestingSuffix) : nestingSuffixEscaped || regexEscape(')');\n    this.nestingOptionsSeparator = nestingOptionsSeparator || ',';\n    this.maxReplaces = maxReplaces || 1000;\n    this.alwaysFormat = alwaysFormat !== undefined ? alwaysFormat : false;\n    this.resetRegExp();\n  }\n  reset() {\n    if (this.options) this.init(this.options);\n  }\n  resetRegExp() {\n    const getOrResetRegExp = (existingRegExp, pattern) => {\n      if (existingRegExp?.source === pattern) {\n        existingRegExp.lastIndex = 0;\n        return existingRegExp;\n      }\n      return new RegExp(pattern, 'g');\n    };\n    this.regexp = getOrResetRegExp(this.regexp, `${this.prefix}(.+?)${this.suffix}`);\n    this.regexpUnescape = getOrResetRegExp(this.regexpUnescape, `${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`);\n    this.nestingRegexp = getOrResetRegExp(this.nestingRegexp, `${this.nestingPrefix}(.+?)${this.nestingSuffix}`);\n  }\n  interpolate(str, data, lng, options) {\n    let match;\n    let value;\n    let replaces;\n    const defaultData = this.options && this.options.interpolation && this.options.interpolation.defaultVariables || {};\n    const handleFormat = key => {\n      if (key.indexOf(this.formatSeparator) < 0) {\n        const path = deepFindWithDefaults(data, defaultData, key, this.options.keySeparator, this.options.ignoreJSONStructure);\n        return this.alwaysFormat ? this.format(path, undefined, lng, {\n          ...options,\n          ...data,\n          interpolationkey: key\n        }) : path;\n      }\n      const p = key.split(this.formatSeparator);\n      const k = p.shift().trim();\n      const f = p.join(this.formatSeparator).trim();\n      return this.format(deepFindWithDefaults(data, defaultData, k, this.options.keySeparator, this.options.ignoreJSONStructure), f, lng, {\n        ...options,\n        ...data,\n        interpolationkey: k\n      });\n    };\n    this.resetRegExp();\n    const missingInterpolationHandler = options?.missingInterpolationHandler || this.options.missingInterpolationHandler;\n    const skipOnVariables = options?.interpolation?.skipOnVariables !== undefined ? options.interpolation.skipOnVariables : this.options.interpolation.skipOnVariables;\n    const todos = [{\n      regex: this.regexpUnescape,\n      safeValue: val => regexSafe(val)\n    }, {\n      regex: this.regexp,\n      safeValue: val => this.escapeValue ? regexSafe(this.escape(val)) : regexSafe(val)\n    }];\n    todos.forEach(todo => {\n      replaces = 0;\n      while (match = todo.regex.exec(str)) {\n        const matchedVar = match[1].trim();\n        value = handleFormat(matchedVar);\n        if (value === undefined) {\n          if (typeof missingInterpolationHandler === 'function') {\n            const temp = missingInterpolationHandler(str, match, options);\n            value = isString(temp) ? temp : '';\n          } else if (options && Object.prototype.hasOwnProperty.call(options, matchedVar)) {\n            value = '';\n          } else if (skipOnVariables) {\n            value = match[0];\n            continue;\n          } else {\n            this.logger.warn(`missed to pass in variable ${matchedVar} for interpolating ${str}`);\n            value = '';\n          }\n        } else if (!isString(value) && !this.useRawValueToEscape) {\n          value = makeString(value);\n        }\n        const safeValue = todo.safeValue(value);\n        str = str.replace(match[0], safeValue);\n        if (skipOnVariables) {\n          todo.regex.lastIndex += value.length;\n          todo.regex.lastIndex -= match[0].length;\n        } else {\n          todo.regex.lastIndex = 0;\n        }\n        replaces++;\n        if (replaces >= this.maxReplaces) {\n          break;\n        }\n      }\n    });\n    return str;\n  }\n  nest(str, fc, options = {}) {\n    let match;\n    let value;\n    let clonedOptions;\n    const handleHasOptions = (key, inheritedOptions) => {\n      const sep = this.nestingOptionsSeparator;\n      if (key.indexOf(sep) < 0) return key;\n      const c = key.split(new RegExp(`${sep}[ ]*{`));\n      let optionsString = `{${c[1]}`;\n      key = c[0];\n      optionsString = this.interpolate(optionsString, clonedOptions);\n      const matchedSingleQuotes = optionsString.match(/'/g);\n      const matchedDoubleQuotes = optionsString.match(/\"/g);\n      if ((matchedSingleQuotes?.length ?? 0) % 2 === 0 && !matchedDoubleQuotes || matchedDoubleQuotes.length % 2 !== 0) {\n        optionsString = optionsString.replace(/'/g, '\"');\n      }\n      try {\n        clonedOptions = JSON.parse(optionsString);\n        if (inheritedOptions) clonedOptions = {\n          ...inheritedOptions,\n          ...clonedOptions\n        };\n      } catch (e) {\n        this.logger.warn(`failed parsing options string in nesting for key ${key}`, e);\n        return `${key}${sep}${optionsString}`;\n      }\n      if (clonedOptions.defaultValue && clonedOptions.defaultValue.indexOf(this.prefix) > -1) delete clonedOptions.defaultValue;\n      return key;\n    };\n    while (match = this.nestingRegexp.exec(str)) {\n      let formatters = [];\n      clonedOptions = {\n        ...options\n      };\n      clonedOptions = clonedOptions.replace && !isString(clonedOptions.replace) ? clonedOptions.replace : clonedOptions;\n      clonedOptions.applyPostProcessor = false;\n      delete clonedOptions.defaultValue;\n      let doReduce = false;\n      if (match[0].indexOf(this.formatSeparator) !== -1 && !/{.*}/.test(match[1])) {\n        const r = match[1].split(this.formatSeparator).map(elem => elem.trim());\n        match[1] = r.shift();\n        formatters = r;\n        doReduce = true;\n      }\n      value = fc(handleHasOptions.call(this, match[1].trim(), clonedOptions), clonedOptions);\n      if (value && match[0] === str && !isString(value)) return value;\n      if (!isString(value)) value = makeString(value);\n      if (!value) {\n        this.logger.warn(`missed to resolve ${match[1]} for nesting ${str}`);\n        value = '';\n      }\n      if (doReduce) {\n        value = formatters.reduce((v, f) => this.format(v, f, options.lng, {\n          ...options,\n          interpolationkey: match[1].trim()\n        }), value.trim());\n      }\n      str = str.replace(match[0], value);\n      this.regexp.lastIndex = 0;\n    }\n    return str;\n  }\n}\n\nconst parseFormatStr = formatStr => {\n  let formatName = formatStr.toLowerCase().trim();\n  const formatOptions = {};\n  if (formatStr.indexOf('(') > -1) {\n    const p = formatStr.split('(');\n    formatName = p[0].toLowerCase().trim();\n    const optStr = p[1].substring(0, p[1].length - 1);\n    if (formatName === 'currency' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.currency) formatOptions.currency = optStr.trim();\n    } else if (formatName === 'relativetime' && optStr.indexOf(':') < 0) {\n      if (!formatOptions.range) formatOptions.range = optStr.trim();\n    } else {\n      const opts = optStr.split(';');\n      opts.forEach(opt => {\n        if (opt) {\n          const [key, ...rest] = opt.split(':');\n          const val = rest.join(':').trim().replace(/^'+|'+$/g, '');\n          const trimmedKey = key.trim();\n          if (!formatOptions[trimmedKey]) formatOptions[trimmedKey] = val;\n          if (val === 'false') formatOptions[trimmedKey] = false;\n          if (val === 'true') formatOptions[trimmedKey] = true;\n          if (!isNaN(val)) formatOptions[trimmedKey] = parseInt(val, 10);\n        }\n      });\n    }\n  }\n  return {\n    formatName,\n    formatOptions\n  };\n};\nconst createCachedFormatter = fn => {\n  const cache = {};\n  return (v, l, o) => {\n    let optForCache = o;\n    if (o && o.interpolationkey && o.formatParams && o.formatParams[o.interpolationkey] && o[o.interpolationkey]) {\n      optForCache = {\n        ...optForCache,\n        [o.interpolationkey]: undefined\n      };\n    }\n    const key = l + JSON.stringify(optForCache);\n    let frm = cache[key];\n    if (!frm) {\n      frm = fn(getCleanedCode(l), o);\n      cache[key] = frm;\n    }\n    return frm(v);\n  };\n};\nconst createNonCachedFormatter = fn => (v, l, o) => fn(getCleanedCode(l), o)(v);\nclass Formatter {\n  constructor(options = {}) {\n    this.logger = baseLogger.create('formatter');\n    this.options = options;\n    this.init(options);\n  }\n  init(services, options = {\n    interpolation: {}\n  }) {\n    this.formatSeparator = options.interpolation.formatSeparator || ',';\n    const cf = options.cacheInBuiltFormats ? createCachedFormatter : createNonCachedFormatter;\n    this.formats = {\n      number: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      currency: cf((lng, opt) => {\n        const formatter = new Intl.NumberFormat(lng, {\n          ...opt,\n          style: 'currency'\n        });\n        return val => formatter.format(val);\n      }),\n      datetime: cf((lng, opt) => {\n        const formatter = new Intl.DateTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      }),\n      relativetime: cf((lng, opt) => {\n        const formatter = new Intl.RelativeTimeFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val, opt.range || 'day');\n      }),\n      list: cf((lng, opt) => {\n        const formatter = new Intl.ListFormat(lng, {\n          ...opt\n        });\n        return val => formatter.format(val);\n      })\n    };\n  }\n  add(name, fc) {\n    this.formats[name.toLowerCase().trim()] = fc;\n  }\n  addCached(name, fc) {\n    this.formats[name.toLowerCase().trim()] = createCachedFormatter(fc);\n  }\n  format(value, format, lng, options = {}) {\n    const formats = format.split(this.formatSeparator);\n    if (formats.length > 1 && formats[0].indexOf('(') > 1 && formats[0].indexOf(')') < 0 && formats.find(f => f.indexOf(')') > -1)) {\n      const lastIndex = formats.findIndex(f => f.indexOf(')') > -1);\n      formats[0] = [formats[0], ...formats.splice(1, lastIndex)].join(this.formatSeparator);\n    }\n    const result = formats.reduce((mem, f) => {\n      const {\n        formatName,\n        formatOptions\n      } = parseFormatStr(f);\n      if (this.formats[formatName]) {\n        let formatted = mem;\n        try {\n          const valOptions = options?.formatParams?.[options.interpolationkey] || {};\n          const l = valOptions.locale || valOptions.lng || options.locale || options.lng || lng;\n          formatted = this.formats[formatName](mem, l, {\n            ...formatOptions,\n            ...options,\n            ...valOptions\n          });\n        } catch (error) {\n          this.logger.warn(error);\n        }\n        return formatted;\n      } else {\n        this.logger.warn(`there was no format function for ${formatName}`);\n      }\n      return mem;\n    }, value);\n    return result;\n  }\n}\n\nconst removePending = (q, name) => {\n  if (q.pending[name] !== undefined) {\n    delete q.pending[name];\n    q.pendingCount--;\n  }\n};\nclass Connector extends EventEmitter {\n  constructor(backend, store, services, options = {}) {\n    super();\n    this.backend = backend;\n    this.store = store;\n    this.services = services;\n    this.languageUtils = services.languageUtils;\n    this.options = options;\n    this.logger = baseLogger.create('backendConnector');\n    this.waitingReads = [];\n    this.maxParallelReads = options.maxParallelReads || 10;\n    this.readingCalls = 0;\n    this.maxRetries = options.maxRetries >= 0 ? options.maxRetries : 5;\n    this.retryTimeout = options.retryTimeout >= 1 ? options.retryTimeout : 350;\n    this.state = {};\n    this.queue = [];\n    this.backend?.init?.(services, options.backend, options);\n  }\n  queueLoad(languages, namespaces, options, callback) {\n    const toLoad = {};\n    const pending = {};\n    const toLoadLanguages = {};\n    const toLoadNamespaces = {};\n    languages.forEach(lng => {\n      let hasAllNamespaces = true;\n      namespaces.forEach(ns => {\n        const name = `${lng}|${ns}`;\n        if (!options.reload && this.store.hasResourceBundle(lng, ns)) {\n          this.state[name] = 2;\n        } else if (this.state[name] < 0) ; else if (this.state[name] === 1) {\n          if (pending[name] === undefined) pending[name] = true;\n        } else {\n          this.state[name] = 1;\n          hasAllNamespaces = false;\n          if (pending[name] === undefined) pending[name] = true;\n          if (toLoad[name] === undefined) toLoad[name] = true;\n          if (toLoadNamespaces[ns] === undefined) toLoadNamespaces[ns] = true;\n        }\n      });\n      if (!hasAllNamespaces) toLoadLanguages[lng] = true;\n    });\n    if (Object.keys(toLoad).length || Object.keys(pending).length) {\n      this.queue.push({\n        pending,\n        pendingCount: Object.keys(pending).length,\n        loaded: {},\n        errors: [],\n        callback\n      });\n    }\n    return {\n      toLoad: Object.keys(toLoad),\n      pending: Object.keys(pending),\n      toLoadLanguages: Object.keys(toLoadLanguages),\n      toLoadNamespaces: Object.keys(toLoadNamespaces)\n    };\n  }\n  loaded(name, err, data) {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    if (err) this.emit('failedLoading', lng, ns, err);\n    if (!err && data) {\n      this.store.addResourceBundle(lng, ns, data, undefined, undefined, {\n        skipCopy: true\n      });\n    }\n    this.state[name] = err ? -1 : 2;\n    if (err && data) this.state[name] = 0;\n    const loaded = {};\n    this.queue.forEach(q => {\n      pushPath(q.loaded, [lng], ns);\n      removePending(q, name);\n      if (err) q.errors.push(err);\n      if (q.pendingCount === 0 && !q.done) {\n        Object.keys(q.loaded).forEach(l => {\n          if (!loaded[l]) loaded[l] = {};\n          const loadedKeys = q.loaded[l];\n          if (loadedKeys.length) {\n            loadedKeys.forEach(n => {\n              if (loaded[l][n] === undefined) loaded[l][n] = true;\n            });\n          }\n        });\n        q.done = true;\n        if (q.errors.length) {\n          q.callback(q.errors);\n        } else {\n          q.callback();\n        }\n      }\n    });\n    this.emit('loaded', loaded);\n    this.queue = this.queue.filter(q => !q.done);\n  }\n  read(lng, ns, fcName, tried = 0, wait = this.retryTimeout, callback) {\n    if (!lng.length) return callback(null, {});\n    if (this.readingCalls >= this.maxParallelReads) {\n      this.waitingReads.push({\n        lng,\n        ns,\n        fcName,\n        tried,\n        wait,\n        callback\n      });\n      return;\n    }\n    this.readingCalls++;\n    const resolver = (err, data) => {\n      this.readingCalls--;\n      if (this.waitingReads.length > 0) {\n        const next = this.waitingReads.shift();\n        this.read(next.lng, next.ns, next.fcName, next.tried, next.wait, next.callback);\n      }\n      if (err && data && tried < this.maxRetries) {\n        setTimeout(() => {\n          this.read.call(this, lng, ns, fcName, tried + 1, wait * 2, callback);\n        }, wait);\n        return;\n      }\n      callback(err, data);\n    };\n    const fc = this.backend[fcName].bind(this.backend);\n    if (fc.length === 2) {\n      try {\n        const r = fc(lng, ns);\n        if (r && typeof r.then === 'function') {\n          r.then(data => resolver(null, data)).catch(resolver);\n        } else {\n          resolver(null, r);\n        }\n      } catch (err) {\n        resolver(err);\n      }\n      return;\n    }\n    return fc(lng, ns, resolver);\n  }\n  prepareLoading(languages, namespaces, options = {}, callback) {\n    if (!this.backend) {\n      this.logger.warn('No backend was added via i18next.use. Will not load resources.');\n      return callback && callback();\n    }\n    if (isString(languages)) languages = this.languageUtils.toResolveHierarchy(languages);\n    if (isString(namespaces)) namespaces = [namespaces];\n    const toLoad = this.queueLoad(languages, namespaces, options, callback);\n    if (!toLoad.toLoad.length) {\n      if (!toLoad.pending.length) callback();\n      return null;\n    }\n    toLoad.toLoad.forEach(name => {\n      this.loadOne(name);\n    });\n  }\n  load(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {}, callback);\n  }\n  reload(languages, namespaces, callback) {\n    this.prepareLoading(languages, namespaces, {\n      reload: true\n    }, callback);\n  }\n  loadOne(name, prefix = '') {\n    const s = name.split('|');\n    const lng = s[0];\n    const ns = s[1];\n    this.read(lng, ns, 'read', undefined, undefined, (err, data) => {\n      if (err) this.logger.warn(`${prefix}loading namespace ${ns} for language ${lng} failed`, err);\n      if (!err && data) this.logger.log(`${prefix}loaded namespace ${ns} for language ${lng}`, data);\n      this.loaded(name, err, data);\n    });\n  }\n  saveMissing(languages, namespace, key, fallbackValue, isUpdate, options = {}, clb = () => {}) {\n    if (this.services?.utils?.hasLoadedNamespace && !this.services?.utils?.hasLoadedNamespace(namespace)) {\n      this.logger.warn(`did not save key \"${key}\" as the namespace \"${namespace}\" was not yet loaded`, 'This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!');\n      return;\n    }\n    if (key === undefined || key === null || key === '') return;\n    if (this.backend?.create) {\n      const opts = {\n        ...options,\n        isUpdate\n      };\n      const fc = this.backend.create.bind(this.backend);\n      if (fc.length < 6) {\n        try {\n          let r;\n          if (fc.length === 5) {\n            r = fc(languages, namespace, key, fallbackValue, opts);\n          } else {\n            r = fc(languages, namespace, key, fallbackValue);\n          }\n          if (r && typeof r.then === 'function') {\n            r.then(data => clb(null, data)).catch(clb);\n          } else {\n            clb(null, r);\n          }\n        } catch (err) {\n          clb(err);\n        }\n      } else {\n        fc(languages, namespace, key, fallbackValue, clb, opts);\n      }\n    }\n    if (!languages || !languages[0]) return;\n    this.store.addResource(languages[0], namespace, key, fallbackValue);\n  }\n}\n\nconst get = () => ({\n  debug: false,\n  initAsync: true,\n  ns: ['translation'],\n  defaultNS: ['translation'],\n  fallbackLng: ['dev'],\n  fallbackNS: false,\n  supportedLngs: false,\n  nonExplicitSupportedLngs: false,\n  load: 'all',\n  preload: false,\n  simplifyPluralSuffix: true,\n  keySeparator: '.',\n  nsSeparator: ':',\n  pluralSeparator: '_',\n  contextSeparator: '_',\n  partialBundledLanguages: false,\n  saveMissing: false,\n  updateMissing: false,\n  saveMissingTo: 'fallback',\n  saveMissingPlurals: true,\n  missingKeyHandler: false,\n  missingInterpolationHandler: false,\n  postProcess: false,\n  postProcessPassResolved: false,\n  returnNull: false,\n  returnEmptyString: true,\n  returnObjects: false,\n  joinArrays: false,\n  returnedObjectHandler: false,\n  parseMissingKeyHandler: false,\n  appendNamespaceToMissingKey: false,\n  appendNamespaceToCIMode: false,\n  overloadTranslationOptionHandler: args => {\n    let ret = {};\n    if (typeof args[1] === 'object') ret = args[1];\n    if (isString(args[1])) ret.defaultValue = args[1];\n    if (isString(args[2])) ret.tDescription = args[2];\n    if (typeof args[2] === 'object' || typeof args[3] === 'object') {\n      const options = args[3] || args[2];\n      Object.keys(options).forEach(key => {\n        ret[key] = options[key];\n      });\n    }\n    return ret;\n  },\n  interpolation: {\n    escapeValue: true,\n    format: value => value,\n    prefix: '{{',\n    suffix: '}}',\n    formatSeparator: ',',\n    unescapePrefix: '-',\n    nestingPrefix: '$t(',\n    nestingSuffix: ')',\n    nestingOptionsSeparator: ',',\n    maxReplaces: 1000,\n    skipOnVariables: true\n  },\n  cacheInBuiltFormats: true\n});\nconst transformOptions = options => {\n  if (isString(options.ns)) options.ns = [options.ns];\n  if (isString(options.fallbackLng)) options.fallbackLng = [options.fallbackLng];\n  if (isString(options.fallbackNS)) options.fallbackNS = [options.fallbackNS];\n  if (options.supportedLngs?.indexOf?.('cimode') < 0) {\n    options.supportedLngs = options.supportedLngs.concat(['cimode']);\n  }\n  if (typeof options.initImmediate === 'boolean') options.initAsync = options.initImmediate;\n  return options;\n};\n\nconst noop = () => {};\nconst bindMemberFunctions = inst => {\n  const mems = Object.getOwnPropertyNames(Object.getPrototypeOf(inst));\n  mems.forEach(mem => {\n    if (typeof inst[mem] === 'function') {\n      inst[mem] = inst[mem].bind(inst);\n    }\n  });\n};\nclass I18n extends EventEmitter {\n  constructor(options = {}, callback) {\n    super();\n    this.options = transformOptions(options);\n    this.services = {};\n    this.logger = baseLogger;\n    this.modules = {\n      external: []\n    };\n    bindMemberFunctions(this);\n    if (callback && !this.isInitialized && !options.isClone) {\n      if (!this.options.initAsync) {\n        this.init(options, callback);\n        return this;\n      }\n      setTimeout(() => {\n        this.init(options, callback);\n      }, 0);\n    }\n  }\n  init(options = {}, callback) {\n    this.isInitializing = true;\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n    if (options.defaultNS == null && options.ns) {\n      if (isString(options.ns)) {\n        options.defaultNS = options.ns;\n      } else if (options.ns.indexOf('translation') < 0) {\n        options.defaultNS = options.ns[0];\n      }\n    }\n    const defOpts = get();\n    this.options = {\n      ...defOpts,\n      ...this.options,\n      ...transformOptions(options)\n    };\n    this.options.interpolation = {\n      ...defOpts.interpolation,\n      ...this.options.interpolation\n    };\n    if (options.keySeparator !== undefined) {\n      this.options.userDefinedKeySeparator = options.keySeparator;\n    }\n    if (options.nsSeparator !== undefined) {\n      this.options.userDefinedNsSeparator = options.nsSeparator;\n    }\n    const createClassOnDemand = ClassOrObject => {\n      if (!ClassOrObject) return null;\n      if (typeof ClassOrObject === 'function') return new ClassOrObject();\n      return ClassOrObject;\n    };\n    if (!this.options.isClone) {\n      if (this.modules.logger) {\n        baseLogger.init(createClassOnDemand(this.modules.logger), this.options);\n      } else {\n        baseLogger.init(null, this.options);\n      }\n      let formatter;\n      if (this.modules.formatter) {\n        formatter = this.modules.formatter;\n      } else {\n        formatter = Formatter;\n      }\n      const lu = new LanguageUtil(this.options);\n      this.store = new ResourceStore(this.options.resources, this.options);\n      const s = this.services;\n      s.logger = baseLogger;\n      s.resourceStore = this.store;\n      s.languageUtils = lu;\n      s.pluralResolver = new PluralResolver(lu, {\n        prepend: this.options.pluralSeparator,\n        simplifyPluralSuffix: this.options.simplifyPluralSuffix\n      });\n      if (formatter && (!this.options.interpolation.format || this.options.interpolation.format === defOpts.interpolation.format)) {\n        s.formatter = createClassOnDemand(formatter);\n        s.formatter.init(s, this.options);\n        this.options.interpolation.format = s.formatter.format.bind(s.formatter);\n      }\n      s.interpolator = new Interpolator(this.options);\n      s.utils = {\n        hasLoadedNamespace: this.hasLoadedNamespace.bind(this)\n      };\n      s.backendConnector = new Connector(createClassOnDemand(this.modules.backend), s.resourceStore, s, this.options);\n      s.backendConnector.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      if (this.modules.languageDetector) {\n        s.languageDetector = createClassOnDemand(this.modules.languageDetector);\n        if (s.languageDetector.init) s.languageDetector.init(s, this.options.detection, this.options);\n      }\n      if (this.modules.i18nFormat) {\n        s.i18nFormat = createClassOnDemand(this.modules.i18nFormat);\n        if (s.i18nFormat.init) s.i18nFormat.init(this);\n      }\n      this.translator = new Translator(this.services, this.options);\n      this.translator.on('*', (event, ...args) => {\n        this.emit(event, ...args);\n      });\n      this.modules.external.forEach(m => {\n        if (m.init) m.init(this);\n      });\n    }\n    this.format = this.options.interpolation.format;\n    if (!callback) callback = noop;\n    if (this.options.fallbackLng && !this.services.languageDetector && !this.options.lng) {\n      const codes = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n      if (codes.length > 0 && codes[0] !== 'dev') this.options.lng = codes[0];\n    }\n    if (!this.services.languageDetector && !this.options.lng) {\n      this.logger.warn('init: no languageDetector is used and no lng is defined');\n    }\n    const storeApi = ['getResource', 'hasResourceBundle', 'getResourceBundle', 'getDataByLanguage'];\n    storeApi.forEach(fcName => {\n      this[fcName] = (...args) => this.store[fcName](...args);\n    });\n    const storeApiChained = ['addResource', 'addResources', 'addResourceBundle', 'removeResourceBundle'];\n    storeApiChained.forEach(fcName => {\n      this[fcName] = (...args) => {\n        this.store[fcName](...args);\n        return this;\n      };\n    });\n    const deferred = defer();\n    const load = () => {\n      const finish = (err, t) => {\n        this.isInitializing = false;\n        if (this.isInitialized && !this.initializedStoreOnce) this.logger.warn('init: i18next is already initialized. You should call init just once!');\n        this.isInitialized = true;\n        if (!this.options.isClone) this.logger.log('initialized', this.options);\n        this.emit('initialized', this.options);\n        deferred.resolve(t);\n        callback(err, t);\n      };\n      if (this.languages && !this.isInitialized) return finish(null, this.t.bind(this));\n      this.changeLanguage(this.options.lng, finish);\n    };\n    if (this.options.resources || !this.options.initAsync) {\n      load();\n    } else {\n      setTimeout(load, 0);\n    }\n    return deferred;\n  }\n  loadResources(language, callback = noop) {\n    let usedCallback = callback;\n    const usedLng = isString(language) ? language : this.language;\n    if (typeof language === 'function') usedCallback = language;\n    if (!this.options.resources || this.options.partialBundledLanguages) {\n      if (usedLng?.toLowerCase() === 'cimode' && (!this.options.preload || this.options.preload.length === 0)) return usedCallback();\n      const toLoad = [];\n      const append = lng => {\n        if (!lng) return;\n        if (lng === 'cimode') return;\n        const lngs = this.services.languageUtils.toResolveHierarchy(lng);\n        lngs.forEach(l => {\n          if (l === 'cimode') return;\n          if (toLoad.indexOf(l) < 0) toLoad.push(l);\n        });\n      };\n      if (!usedLng) {\n        const fallbacks = this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);\n        fallbacks.forEach(l => append(l));\n      } else {\n        append(usedLng);\n      }\n      this.options.preload?.forEach?.(l => append(l));\n      this.services.backendConnector.load(toLoad, this.options.ns, e => {\n        if (!e && !this.resolvedLanguage && this.language) this.setResolvedLanguage(this.language);\n        usedCallback(e);\n      });\n    } else {\n      usedCallback(null);\n    }\n  }\n  reloadResources(lngs, ns, callback) {\n    const deferred = defer();\n    if (typeof lngs === 'function') {\n      callback = lngs;\n      lngs = undefined;\n    }\n    if (typeof ns === 'function') {\n      callback = ns;\n      ns = undefined;\n    }\n    if (!lngs) lngs = this.languages;\n    if (!ns) ns = this.options.ns;\n    if (!callback) callback = noop;\n    this.services.backendConnector.reload(lngs, ns, err => {\n      deferred.resolve();\n      callback(err);\n    });\n    return deferred;\n  }\n  use(module) {\n    if (!module) throw new Error('You are passing an undefined module! Please check the object you are passing to i18next.use()');\n    if (!module.type) throw new Error('You are passing a wrong module! Please check the object you are passing to i18next.use()');\n    if (module.type === 'backend') {\n      this.modules.backend = module;\n    }\n    if (module.type === 'logger' || module.log && module.warn && module.error) {\n      this.modules.logger = module;\n    }\n    if (module.type === 'languageDetector') {\n      this.modules.languageDetector = module;\n    }\n    if (module.type === 'i18nFormat') {\n      this.modules.i18nFormat = module;\n    }\n    if (module.type === 'postProcessor') {\n      postProcessor.addPostProcessor(module);\n    }\n    if (module.type === 'formatter') {\n      this.modules.formatter = module;\n    }\n    if (module.type === '3rdParty') {\n      this.modules.external.push(module);\n    }\n    return this;\n  }\n  setResolvedLanguage(l) {\n    if (!l || !this.languages) return;\n    if (['cimode', 'dev'].indexOf(l) > -1) return;\n    for (let li = 0; li < this.languages.length; li++) {\n      const lngInLngs = this.languages[li];\n      if (['cimode', 'dev'].indexOf(lngInLngs) > -1) continue;\n      if (this.store.hasLanguageSomeTranslations(lngInLngs)) {\n        this.resolvedLanguage = lngInLngs;\n        break;\n      }\n    }\n    if (!this.resolvedLanguage && this.languages.indexOf(l) < 0 && this.store.hasLanguageSomeTranslations(l)) {\n      this.resolvedLanguage = l;\n      this.languages.unshift(l);\n    }\n  }\n  changeLanguage(lng, callback) {\n    this.isLanguageChangingTo = lng;\n    const deferred = defer();\n    this.emit('languageChanging', lng);\n    const setLngProps = l => {\n      this.language = l;\n      this.languages = this.services.languageUtils.toResolveHierarchy(l);\n      this.resolvedLanguage = undefined;\n      this.setResolvedLanguage(l);\n    };\n    const done = (err, l) => {\n      if (l) {\n        if (this.isLanguageChangingTo === lng) {\n          setLngProps(l);\n          this.translator.changeLanguage(l);\n          this.isLanguageChangingTo = undefined;\n          this.emit('languageChanged', l);\n          this.logger.log('languageChanged', l);\n        }\n      } else {\n        this.isLanguageChangingTo = undefined;\n      }\n      deferred.resolve((...args) => this.t(...args));\n      if (callback) callback(err, (...args) => this.t(...args));\n    };\n    const setLng = lngs => {\n      if (!lng && !lngs && this.services.languageDetector) lngs = [];\n      const fl = isString(lngs) ? lngs : lngs && lngs[0];\n      const l = this.store.hasLanguageSomeTranslations(fl) ? fl : this.services.languageUtils.getBestMatchFromCodes(isString(lngs) ? [lngs] : lngs);\n      if (l) {\n        if (!this.language) {\n          setLngProps(l);\n        }\n        if (!this.translator.language) this.translator.changeLanguage(l);\n        this.services.languageDetector?.cacheUserLanguage?.(l);\n      }\n      this.loadResources(l, err => {\n        done(err, l);\n      });\n    };\n    if (!lng && this.services.languageDetector && !this.services.languageDetector.async) {\n      setLng(this.services.languageDetector.detect());\n    } else if (!lng && this.services.languageDetector && this.services.languageDetector.async) {\n      if (this.services.languageDetector.detect.length === 0) {\n        this.services.languageDetector.detect().then(setLng);\n      } else {\n        this.services.languageDetector.detect(setLng);\n      }\n    } else {\n      setLng(lng);\n    }\n    return deferred;\n  }\n  getFixedT(lng, ns, keyPrefix) {\n    const fixedT = (key, opts, ...rest) => {\n      let o;\n      if (typeof opts !== 'object') {\n        o = this.options.overloadTranslationOptionHandler([key, opts].concat(rest));\n      } else {\n        o = {\n          ...opts\n        };\n      }\n      o.lng = o.lng || fixedT.lng;\n      o.lngs = o.lngs || fixedT.lngs;\n      o.ns = o.ns || fixedT.ns;\n      if (o.keyPrefix !== '') o.keyPrefix = o.keyPrefix || keyPrefix || fixedT.keyPrefix;\n      const keySeparator = this.options.keySeparator || '.';\n      let resultKey;\n      if (o.keyPrefix && Array.isArray(key)) {\n        resultKey = key.map(k => `${o.keyPrefix}${keySeparator}${k}`);\n      } else {\n        resultKey = o.keyPrefix ? `${o.keyPrefix}${keySeparator}${key}` : key;\n      }\n      return this.t(resultKey, o);\n    };\n    if (isString(lng)) {\n      fixedT.lng = lng;\n    } else {\n      fixedT.lngs = lng;\n    }\n    fixedT.ns = ns;\n    fixedT.keyPrefix = keyPrefix;\n    return fixedT;\n  }\n  t(...args) {\n    return this.translator?.translate(...args);\n  }\n  exists(...args) {\n    return this.translator?.exists(...args);\n  }\n  setDefaultNamespace(ns) {\n    this.options.defaultNS = ns;\n  }\n  hasLoadedNamespace(ns, options = {}) {\n    if (!this.isInitialized) {\n      this.logger.warn('hasLoadedNamespace: i18next was not initialized', this.languages);\n      return false;\n    }\n    if (!this.languages || !this.languages.length) {\n      this.logger.warn('hasLoadedNamespace: i18n.languages were undefined or empty', this.languages);\n      return false;\n    }\n    const lng = options.lng || this.resolvedLanguage || this.languages[0];\n    const fallbackLng = this.options ? this.options.fallbackLng : false;\n    const lastLng = this.languages[this.languages.length - 1];\n    if (lng.toLowerCase() === 'cimode') return true;\n    const loadNotPending = (l, n) => {\n      const loadState = this.services.backendConnector.state[`${l}|${n}`];\n      return loadState === -1 || loadState === 0 || loadState === 2;\n    };\n    if (options.precheck) {\n      const preResult = options.precheck(this, loadNotPending);\n      if (preResult !== undefined) return preResult;\n    }\n    if (this.hasResourceBundle(lng, ns)) return true;\n    if (!this.services.backendConnector.backend || this.options.resources && !this.options.partialBundledLanguages) return true;\n    if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n    return false;\n  }\n  loadNamespaces(ns, callback) {\n    const deferred = defer();\n    if (!this.options.ns) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    if (isString(ns)) ns = [ns];\n    ns.forEach(n => {\n      if (this.options.ns.indexOf(n) < 0) this.options.ns.push(n);\n    });\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  loadLanguages(lngs, callback) {\n    const deferred = defer();\n    if (isString(lngs)) lngs = [lngs];\n    const preloaded = this.options.preload || [];\n    const newLngs = lngs.filter(lng => preloaded.indexOf(lng) < 0 && this.services.languageUtils.isSupportedCode(lng));\n    if (!newLngs.length) {\n      if (callback) callback();\n      return Promise.resolve();\n    }\n    this.options.preload = preloaded.concat(newLngs);\n    this.loadResources(err => {\n      deferred.resolve();\n      if (callback) callback(err);\n    });\n    return deferred;\n  }\n  dir(lng) {\n    if (!lng) lng = this.resolvedLanguage || (this.languages?.length > 0 ? this.languages[0] : this.language);\n    if (!lng) return 'rtl';\n    const rtlLngs = ['ar', 'shu', 'sqr', 'ssh', 'xaa', 'yhd', 'yud', 'aao', 'abh', 'abv', 'acm', 'acq', 'acw', 'acx', 'acy', 'adf', 'ads', 'aeb', 'aec', 'afb', 'ajp', 'apc', 'apd', 'arb', 'arq', 'ars', 'ary', 'arz', 'auz', 'avl', 'ayh', 'ayl', 'ayn', 'ayp', 'bbz', 'pga', 'he', 'iw', 'ps', 'pbt', 'pbu', 'pst', 'prp', 'prd', 'ug', 'ur', 'ydd', 'yds', 'yih', 'ji', 'yi', 'hbo', 'men', 'xmn', 'fa', 'jpr', 'peo', 'pes', 'prs', 'dv', 'sam', 'ckb'];\n    const languageUtils = this.services?.languageUtils || new LanguageUtil(get());\n    return rtlLngs.indexOf(languageUtils.getLanguagePartFromCode(lng)) > -1 || lng.toLowerCase().indexOf('-arab') > 1 ? 'rtl' : 'ltr';\n  }\n  static createInstance(options = {}, callback) {\n    return new I18n(options, callback);\n  }\n  cloneInstance(options = {}, callback = noop) {\n    const forkResourceStore = options.forkResourceStore;\n    if (forkResourceStore) delete options.forkResourceStore;\n    const mergedOptions = {\n      ...this.options,\n      ...options,\n      ...{\n        isClone: true\n      }\n    };\n    const clone = new I18n(mergedOptions);\n    if (options.debug !== undefined || options.prefix !== undefined) {\n      clone.logger = clone.logger.clone(options);\n    }\n    const membersToCopy = ['store', 'services', 'language'];\n    membersToCopy.forEach(m => {\n      clone[m] = this[m];\n    });\n    clone.services = {\n      ...this.services\n    };\n    clone.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    if (forkResourceStore) {\n      const clonedData = Object.keys(this.store.data).reduce((prev, l) => {\n        prev[l] = {\n          ...this.store.data[l]\n        };\n        prev[l] = Object.keys(prev[l]).reduce((acc, n) => {\n          acc[n] = {\n            ...prev[l][n]\n          };\n          return acc;\n        }, prev[l]);\n        return prev;\n      }, {});\n      clone.store = new ResourceStore(clonedData, mergedOptions);\n      clone.services.resourceStore = clone.store;\n    }\n    clone.translator = new Translator(clone.services, mergedOptions);\n    clone.translator.on('*', (event, ...args) => {\n      clone.emit(event, ...args);\n    });\n    clone.init(mergedOptions, callback);\n    clone.translator.options = mergedOptions;\n    clone.translator.backendConnector.services.utils = {\n      hasLoadedNamespace: clone.hasLoadedNamespace.bind(clone)\n    };\n    return clone;\n  }\n  toJSON() {\n    return {\n      options: this.options,\n      store: this.store,\n      language: this.language,\n      languages: this.languages,\n      resolvedLanguage: this.resolvedLanguage\n    };\n  }\n}\nconst instance = I18n.createInstance();\ninstance.createInstance = I18n.createInstance;\n\nconst createInstance = instance.createInstance;\nconst dir = instance.dir;\nconst init = instance.init;\nconst loadResources = instance.loadResources;\nconst reloadResources = instance.reloadResources;\nconst use = instance.use;\nconst changeLanguage = instance.changeLanguage;\nconst getFixedT = instance.getFixedT;\nconst t = instance.t;\nconst exists = instance.exists;\nconst setDefaultNamespace = instance.setDefaultNamespace;\nconst hasLoadedNamespace = instance.hasLoadedNamespace;\nconst loadNamespaces = instance.loadNamespaces;\nconst loadLanguages = instance.loadLanguages;\n\nexport { changeLanguage, createInstance, instance as default, dir, exists, getFixedT, hasLoadedNamespace, init, loadLanguages, loadNamespaces, loadResources, reloadResources, setDefaultNamespace, t, use };\n"], "mappings": ";;;AAAA,IAAM,WAAW,SAAO,OAAO,QAAQ;AACvC,IAAM,QAAQ,MAAM;AAClB,MAAI;AACJ,MAAI;AACJ,QAAM,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC/C,UAAM;AACN,UAAM;AAAA,EACR,CAAC;AACD,UAAQ,UAAU;AAClB,UAAQ,SAAS;AACjB,SAAO;AACT;AACA,IAAM,aAAa,YAAU;AAC3B,MAAI,UAAU,KAAM,QAAO;AAC3B,SAAO,KAAK;AACd;AACA,IAAM,OAAO,CAAC,GAAG,GAAGA,OAAM;AACxB,IAAE,QAAQ,OAAK;AACb,QAAI,EAAE,CAAC,EAAG,CAAAA,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,EACtB,CAAC;AACH;AACA,IAAM,4BAA4B;AAClC,IAAM,WAAW,SAAO,OAAO,IAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,QAAQ,2BAA2B,GAAG,IAAI;AACvG,IAAM,uBAAuB,YAAU,CAAC,UAAU,SAAS,MAAM;AACjE,IAAM,gBAAgB,CAAC,QAAQ,MAAM,UAAU;AAC7C,QAAM,QAAQ,CAAC,SAAS,IAAI,IAAI,OAAO,KAAK,MAAM,GAAG;AACrD,MAAI,aAAa;AACjB,SAAO,aAAa,MAAM,SAAS,GAAG;AACpC,QAAI,qBAAqB,MAAM,EAAG,QAAO,CAAC;AAC1C,UAAM,MAAM,SAAS,MAAM,UAAU,CAAC;AACtC,QAAI,CAAC,OAAO,GAAG,KAAK,MAAO,QAAO,GAAG,IAAI,IAAI,MAAM;AACnD,QAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,eAAS,OAAO,GAAG;AAAA,IACrB,OAAO;AACL,eAAS,CAAC;AAAA,IACZ;AACA,MAAE;AAAA,EACJ;AACA,MAAI,qBAAqB,MAAM,EAAG,QAAO,CAAC;AAC1C,SAAO;AAAA,IACL,KAAK;AAAA,IACL,GAAG,SAAS,MAAM,UAAU,CAAC;AAAA,EAC/B;AACF;AACA,IAAM,UAAU,CAAC,QAAQ,MAAM,aAAa;AAC1C,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,MAAM,MAAM;AACtC,MAAI,QAAQ,UAAa,KAAK,WAAW,GAAG;AAC1C,QAAI,CAAC,IAAI;AACT;AAAA,EACF;AACA,MAAI,IAAI,KAAK,KAAK,SAAS,CAAC;AAC5B,MAAI,IAAI,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AACrC,MAAI,OAAO,cAAc,QAAQ,GAAG,MAAM;AAC1C,SAAO,KAAK,QAAQ,UAAa,EAAE,QAAQ;AACzC,QAAI,GAAG,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI,CAAC;AAC3B,QAAI,EAAE,MAAM,GAAG,EAAE,SAAS,CAAC;AAC3B,WAAO,cAAc,QAAQ,GAAG,MAAM;AACtC,SAAI,6BAAM,QAAO,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,aAAa;AAClE,WAAK,MAAM;AAAA,IACb;AAAA,EACF;AACA,OAAK,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI;AAC/B;AACA,IAAM,WAAW,CAAC,QAAQ,MAAM,UAAU,WAAW;AACnD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,MAAM,MAAM;AACtC,MAAI,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC;AACpB,MAAI,CAAC,EAAE,KAAK,QAAQ;AACtB;AACA,IAAM,UAAU,CAAC,QAAQ,SAAS;AAChC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI,cAAc,QAAQ,IAAI;AAC9B,MAAI,CAAC,IAAK,QAAO;AACjB,MAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,CAAC,EAAG,QAAO;AAC1D,SAAO,IAAI,CAAC;AACd;AACA,IAAM,sBAAsB,CAAC,MAAM,aAAa,QAAQ;AACtD,QAAM,QAAQ,QAAQ,MAAM,GAAG;AAC/B,MAAI,UAAU,QAAW;AACvB,WAAO;AAAA,EACT;AACA,SAAO,QAAQ,aAAa,GAAG;AACjC;AACA,IAAM,aAAa,CAAC,QAAQ,QAAQ,cAAc;AAChD,aAAW,QAAQ,QAAQ;AACzB,QAAI,SAAS,eAAe,SAAS,eAAe;AAClD,UAAI,QAAQ,QAAQ;AAClB,YAAI,SAAS,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,aAAa,UAAU,SAAS,OAAO,IAAI,CAAC,KAAK,OAAO,IAAI,aAAa,QAAQ;AACxH,cAAI,UAAW,QAAO,IAAI,IAAI,OAAO,IAAI;AAAA,QAC3C,OAAO;AACL,qBAAW,OAAO,IAAI,GAAG,OAAO,IAAI,GAAG,SAAS;AAAA,QAClD;AAAA,MACF,OAAO;AACL,eAAO,IAAI,IAAI,OAAO,IAAI;AAAA,MAC5B;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,cAAc,SAAO,IAAI,QAAQ,uCAAuC,MAAM;AACpF,IAAI,aAAa;AAAA,EACf,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AACA,IAAM,SAAS,UAAQ;AACrB,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,KAAK,QAAQ,cAAc,OAAK,WAAW,CAAC,CAAC;AAAA,EACtD;AACA,SAAO;AACT;AACA,IAAM,cAAN,MAAkB;AAAA,EAChB,YAAY,UAAU;AACpB,SAAK,WAAW;AAChB,SAAK,YAAY,oBAAI,IAAI;AACzB,SAAK,cAAc,CAAC;AAAA,EACtB;AAAA,EACA,UAAU,SAAS;AACjB,UAAM,kBAAkB,KAAK,UAAU,IAAI,OAAO;AAClD,QAAI,oBAAoB,QAAW;AACjC,aAAO;AAAA,IACT;AACA,UAAM,YAAY,IAAI,OAAO,OAAO;AACpC,QAAI,KAAK,YAAY,WAAW,KAAK,UAAU;AAC7C,WAAK,UAAU,OAAO,KAAK,YAAY,MAAM,CAAC;AAAA,IAChD;AACA,SAAK,UAAU,IAAI,SAAS,SAAS;AACrC,SAAK,YAAY,KAAK,OAAO;AAC7B,WAAO;AAAA,EACT;AACF;AACA,IAAM,QAAQ,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG;AACtC,IAAM,iCAAiC,IAAI,YAAY,EAAE;AACzD,IAAM,sBAAsB,CAAC,KAAK,aAAa,iBAAiB;AAC9D,gBAAc,eAAe;AAC7B,iBAAe,gBAAgB;AAC/B,QAAM,gBAAgB,MAAM,OAAO,OAAK,YAAY,QAAQ,CAAC,IAAI,KAAK,aAAa,QAAQ,CAAC,IAAI,CAAC;AACjG,MAAI,cAAc,WAAW,EAAG,QAAO;AACvC,QAAM,IAAI,+BAA+B,UAAU,IAAI,cAAc,IAAI,OAAK,MAAM,MAAM,QAAQ,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG;AACjH,MAAI,UAAU,CAAC,EAAE,KAAK,GAAG;AACzB,MAAI,CAAC,SAAS;AACZ,UAAM,KAAK,IAAI,QAAQ,YAAY;AACnC,QAAI,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,UAAU,GAAG,EAAE,CAAC,GAAG;AAC3C,gBAAU;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAM,WAAW,CAAC,KAAK,MAAM,eAAe,QAAQ;AAClD,MAAI,CAAC,IAAK,QAAO;AACjB,MAAI,IAAI,IAAI,GAAG;AACb,QAAI,CAAC,OAAO,UAAU,eAAe,KAAK,KAAK,IAAI,EAAG,QAAO;AAC7D,WAAO,IAAI,IAAI;AAAA,EACjB;AACA,QAAM,SAAS,KAAK,MAAM,YAAY;AACtC,MAAI,UAAU;AACd,WAAS,IAAI,GAAG,IAAI,OAAO,UAAS;AAClC,QAAI,CAAC,WAAW,OAAO,YAAY,UAAU;AAC3C,aAAO;AAAA,IACT;AACA,QAAI;AACJ,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,EAAE,GAAG;AACtC,UAAI,MAAM,GAAG;AACX,oBAAY;AAAA,MACd;AACA,kBAAY,OAAO,CAAC;AACpB,aAAO,QAAQ,QAAQ;AACvB,UAAI,SAAS,QAAW;AACtB,YAAI,CAAC,UAAU,UAAU,SAAS,EAAE,QAAQ,OAAO,IAAI,IAAI,MAAM,IAAI,OAAO,SAAS,GAAG;AACtF;AAAA,QACF;AACA,aAAK,IAAI,IAAI;AACb;AAAA,MACF;AAAA,IACF;AACA,cAAU;AAAA,EACZ;AACA,SAAO;AACT;AACA,IAAM,iBAAiB,UAAQ,6BAAM,QAAQ,KAAK;AAElD,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,IAAI,MAAM;AACR,SAAK,OAAO,OAAO,IAAI;AAAA,EACzB;AAAA,EACA,KAAK,MAAM;AACT,SAAK,OAAO,QAAQ,IAAI;AAAA,EAC1B;AAAA,EACA,MAAM,MAAM;AACV,SAAK,OAAO,SAAS,IAAI;AAAA,EAC3B;AAAA,EACA,OAAO,MAAM,MAAM;AA3MrB;AA4MI,mDAAU,UAAV,mBAAiB,UAAjB,4BAAyB,SAAS;AAAA,EACpC;AACF;AACA,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,YAAY,gBAAgBC,WAAU,CAAC,GAAG;AACxC,SAAK,KAAK,gBAAgBA,QAAO;AAAA,EACnC;AAAA,EACA,KAAK,gBAAgBA,WAAU,CAAC,GAAG;AACjC,SAAK,SAASA,SAAQ,UAAU;AAChC,SAAK,SAAS,kBAAkB;AAChC,SAAK,UAAUA;AACf,SAAK,QAAQA,SAAQ;AAAA,EACvB;AAAA,EACA,OAAO,MAAM;AACX,WAAO,KAAK,QAAQ,MAAM,OAAO,IAAI,IAAI;AAAA,EAC3C;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK,QAAQ,MAAM,QAAQ,IAAI,IAAI;AAAA,EAC5C;AAAA,EACA,SAAS,MAAM;AACb,WAAO,KAAK,QAAQ,MAAM,SAAS,EAAE;AAAA,EACvC;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK,QAAQ,MAAM,QAAQ,wBAAwB,IAAI;AAAA,EAChE;AAAA,EACA,QAAQ,MAAM,KAAK,QAAQ,WAAW;AACpC,QAAI,aAAa,CAAC,KAAK,MAAO,QAAO;AACrC,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,MAAK,CAAC,IAAI,GAAG,MAAM,GAAG,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC;AACnE,WAAO,KAAK,OAAO,GAAG,EAAE,IAAI;AAAA,EAC9B;AAAA,EACA,OAAO,YAAY;AACjB,WAAO,IAAI,QAAO,KAAK,QAAQ;AAAA,MAC7B,GAAG;AAAA,QACD,QAAQ,GAAG,KAAK,MAAM,IAAI,UAAU;AAAA,MACtC;AAAA,MACA,GAAG,KAAK;AAAA,IACV,CAAC;AAAA,EACH;AAAA,EACA,MAAMA,UAAS;AACb,IAAAA,WAAUA,YAAW,KAAK;AAC1B,IAAAA,SAAQ,SAASA,SAAQ,UAAU,KAAK;AACxC,WAAO,IAAI,QAAO,KAAK,QAAQA,QAAO;AAAA,EACxC;AACF;AACA,IAAI,aAAa,IAAI,OAAO;AAE5B,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,SAAK,YAAY,CAAC;AAAA,EACpB;AAAA,EACA,GAAG,QAAQ,UAAU;AACnB,WAAO,MAAM,GAAG,EAAE,QAAQ,WAAS;AACjC,UAAI,CAAC,KAAK,UAAU,KAAK,EAAG,MAAK,UAAU,KAAK,IAAI,oBAAI,IAAI;AAC5D,YAAM,eAAe,KAAK,UAAU,KAAK,EAAE,IAAI,QAAQ,KAAK;AAC5D,WAAK,UAAU,KAAK,EAAE,IAAI,UAAU,eAAe,CAAC;AAAA,IACtD,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,OAAO,UAAU;AACnB,QAAI,CAAC,KAAK,UAAU,KAAK,EAAG;AAC5B,QAAI,CAAC,UAAU;AACb,aAAO,KAAK,UAAU,KAAK;AAC3B;AAAA,IACF;AACA,SAAK,UAAU,KAAK,EAAE,OAAO,QAAQ;AAAA,EACvC;AAAA,EACA,KAAK,UAAU,MAAM;AACnB,QAAI,KAAK,UAAU,KAAK,GAAG;AACzB,YAAM,SAAS,MAAM,KAAK,KAAK,UAAU,KAAK,EAAE,QAAQ,CAAC;AACzD,aAAO,QAAQ,CAAC,CAAC,UAAU,aAAa,MAAM;AAC5C,iBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,mBAAS,GAAG,IAAI;AAAA,QAClB;AAAA,MACF,CAAC;AAAA,IACH;AACA,QAAI,KAAK,UAAU,GAAG,GAAG;AACvB,YAAM,SAAS,MAAM,KAAK,KAAK,UAAU,GAAG,EAAE,QAAQ,CAAC;AACvD,aAAO,QAAQ,CAAC,CAAC,UAAU,aAAa,MAAM;AAC5C,iBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,mBAAS,MAAM,UAAU,CAAC,OAAO,GAAG,IAAI,CAAC;AAAA,QAC3C;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,IAAM,gBAAN,cAA4B,aAAa;AAAA,EACvC,YAAY,MAAMA,WAAU;AAAA,IAC1B,IAAI,CAAC,aAAa;AAAA,IAClB,WAAW;AAAA,EACb,GAAG;AACD,UAAM;AACN,SAAK,OAAO,QAAQ,CAAC;AACrB,SAAK,UAAUA;AACf,QAAI,KAAK,QAAQ,iBAAiB,QAAW;AAC3C,WAAK,QAAQ,eAAe;AAAA,IAC9B;AACA,QAAI,KAAK,QAAQ,wBAAwB,QAAW;AAClD,WAAK,QAAQ,sBAAsB;AAAA,IACrC;AAAA,EACF;AAAA,EACA,cAAc,IAAI;AAChB,QAAI,KAAK,QAAQ,GAAG,QAAQ,EAAE,IAAI,GAAG;AACnC,WAAK,QAAQ,GAAG,KAAK,EAAE;AAAA,IACzB;AAAA,EACF;AAAA,EACA,iBAAiB,IAAI;AACnB,UAAM,QAAQ,KAAK,QAAQ,GAAG,QAAQ,EAAE;AACxC,QAAI,QAAQ,IAAI;AACd,WAAK,QAAQ,GAAG,OAAO,OAAO,CAAC;AAAA,IACjC;AAAA,EACF;AAAA,EACA,YAAY,KAAK,IAAI,KAAKA,WAAU,CAAC,GAAG;AA5T1C;AA6TI,UAAM,eAAeA,SAAQ,iBAAiB,SAAYA,SAAQ,eAAe,KAAK,QAAQ;AAC9F,UAAM,sBAAsBA,SAAQ,wBAAwB,SAAYA,SAAQ,sBAAsB,KAAK,QAAQ;AACnH,QAAI;AACJ,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AAAA,IACtB,OAAO;AACL,aAAO,CAAC,KAAK,EAAE;AACf,UAAI,KAAK;AACP,YAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,eAAK,KAAK,GAAG,GAAG;AAAA,QAClB,WAAW,SAAS,GAAG,KAAK,cAAc;AACxC,eAAK,KAAK,GAAG,IAAI,MAAM,YAAY,CAAC;AAAA,QACtC,OAAO;AACL,eAAK,KAAK,GAAG;AAAA,QACf;AAAA,MACF;AAAA,IACF;AACA,UAAM,SAAS,QAAQ,KAAK,MAAM,IAAI;AACtC,QAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,IAAI,QAAQ,GAAG,IAAI,IAAI;AACnD,YAAM,KAAK,CAAC;AACZ,WAAK,KAAK,CAAC;AACX,YAAM,KAAK,MAAM,CAAC,EAAE,KAAK,GAAG;AAAA,IAC9B;AACA,QAAI,UAAU,CAAC,uBAAuB,CAAC,SAAS,GAAG,EAAG,QAAO;AAC7D,WAAO,UAAS,gBAAK,SAAL,mBAAY,SAAZ,mBAAmB,KAAK,KAAK,YAAY;AAAA,EAC3D;AAAA,EACA,YAAY,KAAK,IAAI,KAAK,OAAOA,WAAU;AAAA,IACzC,QAAQ;AAAA,EACV,GAAG;AACD,UAAM,eAAeA,SAAQ,iBAAiB,SAAYA,SAAQ,eAAe,KAAK,QAAQ;AAC9F,QAAI,OAAO,CAAC,KAAK,EAAE;AACnB,QAAI,IAAK,QAAO,KAAK,OAAO,eAAe,IAAI,MAAM,YAAY,IAAI,GAAG;AACxE,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AACpB,cAAQ;AACR,WAAK,KAAK,CAAC;AAAA,IACb;AACA,SAAK,cAAc,EAAE;AACrB,YAAQ,KAAK,MAAM,MAAM,KAAK;AAC9B,QAAI,CAACA,SAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,KAAK,KAAK;AAAA,EAC7D;AAAA,EACA,aAAa,KAAK,IAAI,WAAWA,WAAU;AAAA,IACzC,QAAQ;AAAA,EACV,GAAG;AACD,eAAW,KAAK,WAAW;AACzB,UAAI,SAAS,UAAU,CAAC,CAAC,KAAK,MAAM,QAAQ,UAAU,CAAC,CAAC,EAAG,MAAK,YAAY,KAAK,IAAI,GAAG,UAAU,CAAC,GAAG;AAAA,QACpG,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,QAAI,CAACA,SAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,SAAS;AAAA,EAC5D;AAAA,EACA,kBAAkB,KAAK,IAAI,WAAW,MAAM,WAAWA,WAAU;AAAA,IAC/D,QAAQ;AAAA,IACR,UAAU;AAAA,EACZ,GAAG;AACD,QAAI,OAAO,CAAC,KAAK,EAAE;AACnB,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,aAAO,IAAI,MAAM,GAAG;AACpB,aAAO;AACP,kBAAY;AACZ,WAAK,KAAK,CAAC;AAAA,IACb;AACA,SAAK,cAAc,EAAE;AACrB,QAAI,OAAO,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC;AACxC,QAAI,CAACA,SAAQ,SAAU,aAAY,KAAK,MAAM,KAAK,UAAU,SAAS,CAAC;AACvE,QAAI,MAAM;AACR,iBAAW,MAAM,WAAW,SAAS;AAAA,IACvC,OAAO;AACL,aAAO;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,IACF;AACA,YAAQ,KAAK,MAAM,MAAM,IAAI;AAC7B,QAAI,CAACA,SAAQ,OAAQ,MAAK,KAAK,SAAS,KAAK,IAAI,SAAS;AAAA,EAC5D;AAAA,EACA,qBAAqB,KAAK,IAAI;AAC5B,QAAI,KAAK,kBAAkB,KAAK,EAAE,GAAG;AACnC,aAAO,KAAK,KAAK,GAAG,EAAE,EAAE;AAAA,IAC1B;AACA,SAAK,iBAAiB,EAAE;AACxB,SAAK,KAAK,WAAW,KAAK,EAAE;AAAA,EAC9B;AAAA,EACA,kBAAkB,KAAK,IAAI;AACzB,WAAO,KAAK,YAAY,KAAK,EAAE,MAAM;AAAA,EACvC;AAAA,EACA,kBAAkB,KAAK,IAAI;AACzB,QAAI,CAAC,GAAI,MAAK,KAAK,QAAQ;AAC3B,WAAO,KAAK,YAAY,KAAK,EAAE;AAAA,EACjC;AAAA,EACA,kBAAkB,KAAK;AACrB,WAAO,KAAK,KAAK,GAAG;AAAA,EACtB;AAAA,EACA,4BAA4B,KAAK;AAC/B,UAAM,OAAO,KAAK,kBAAkB,GAAG;AACvC,UAAM,IAAI,QAAQ,OAAO,KAAK,IAAI,KAAK,CAAC;AACxC,WAAO,CAAC,CAAC,EAAE,KAAK,OAAK,KAAK,CAAC,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC;AAAA,EACjE;AAAA,EACA,SAAS;AACP,WAAO,KAAK;AAAA,EACd;AACF;AAEA,IAAI,gBAAgB;AAAA,EAClB,YAAY,CAAC;AAAA,EACb,iBAAiB,QAAQ;AACvB,SAAK,WAAW,OAAO,IAAI,IAAI;AAAA,EACjC;AAAA,EACA,OAAO,YAAY,OAAO,KAAKA,UAAS,YAAY;AAClD,eAAW,QAAQ,eAAa;AA1apC;AA2aM,gBAAQ,UAAK,WAAW,SAAS,MAAzB,mBAA4B,QAAQ,OAAO,KAAKA,UAAS,gBAAe;AAAA,IAClF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,uBAAuB,SAAO,CAAC,SAAS,GAAG,KAAK,OAAO,QAAQ,aAAa,OAAO,QAAQ;AACjG,IAAM,aAAN,MAAM,oBAAmB,aAAa;AAAA,EACpC,YAAY,UAAUA,WAAU,CAAC,GAAG;AAClC,UAAM;AACN,SAAK,CAAC,iBAAiB,iBAAiB,kBAAkB,gBAAgB,oBAAoB,cAAc,OAAO,GAAG,UAAU,IAAI;AACpI,SAAK,UAAUA;AACf,QAAI,KAAK,QAAQ,iBAAiB,QAAW;AAC3C,WAAK,QAAQ,eAAe;AAAA,IAC9B;AACA,SAAK,SAAS,WAAW,OAAO,YAAY;AAAA,EAC9C;AAAA,EACA,eAAe,KAAK;AAClB,QAAI,IAAK,MAAK,WAAW;AAAA,EAC3B;AAAA,EACA,OAAO,KAAK,IAAI;AAAA,IACd,eAAe,CAAC;AAAA,EAClB,GAAG;AACD,UAAM,MAAM;AAAA,MACV,GAAG;AAAA,IACL;AACA,QAAI,OAAO,KAAM,QAAO;AACxB,UAAM,WAAW,KAAK,QAAQ,KAAK,GAAG;AACtC,YAAO,qCAAU,SAAQ;AAAA,EAC3B;AAAA,EACA,eAAe,KAAK,KAAK;AACvB,QAAI,cAAc,IAAI,gBAAgB,SAAY,IAAI,cAAc,KAAK,QAAQ;AACjF,QAAI,gBAAgB,OAAW,eAAc;AAC7C,UAAM,eAAe,IAAI,iBAAiB,SAAY,IAAI,eAAe,KAAK,QAAQ;AACtF,QAAI,aAAa,IAAI,MAAM,KAAK,QAAQ,aAAa,CAAC;AACtD,UAAM,uBAAuB,eAAe,IAAI,QAAQ,WAAW,IAAI;AACvE,UAAM,uBAAuB,CAAC,KAAK,QAAQ,2BAA2B,CAAC,IAAI,gBAAgB,CAAC,KAAK,QAAQ,0BAA0B,CAAC,IAAI,eAAe,CAAC,oBAAoB,KAAK,aAAa,YAAY;AAC1M,QAAI,wBAAwB,CAAC,sBAAsB;AACjD,YAAM,IAAI,IAAI,MAAM,KAAK,aAAa,aAAa;AACnD,UAAI,KAAK,EAAE,SAAS,GAAG;AACrB,eAAO;AAAA,UACL;AAAA,UACA,YAAY,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI;AAAA,QACpD;AAAA,MACF;AACA,YAAM,QAAQ,IAAI,MAAM,WAAW;AACnC,UAAI,gBAAgB,gBAAgB,gBAAgB,gBAAgB,KAAK,QAAQ,GAAG,QAAQ,MAAM,CAAC,CAAC,IAAI,GAAI,cAAa,MAAM,MAAM;AACrI,YAAM,MAAM,KAAK,YAAY;AAAA,IAC/B;AACA,WAAO;AAAA,MACL;AAAA,MACA,YAAY,SAAS,UAAU,IAAI,CAAC,UAAU,IAAI;AAAA,IACpD;AAAA,EACF;AAAA,EACA,UAAU,MAAM,GAAG,SAAS;AAC1B,QAAI,MAAM,OAAO,MAAM,WAAW;AAAA,MAChC,GAAG;AAAA,IACL,IAAI;AACJ,QAAI,OAAO,QAAQ,YAAY,KAAK,QAAQ,kCAAkC;AAC5E,YAAM,KAAK,QAAQ,iCAAiC,SAAS;AAAA,IAC/D;AACA,QAAI,OAAO,YAAY,SAAU,OAAM;AAAA,MACrC,GAAG;AAAA,IACL;AACA,QAAI,CAAC,IAAK,OAAM,CAAC;AACjB,QAAI,QAAQ,KAAM,QAAO;AACzB,QAAI,CAAC,MAAM,QAAQ,IAAI,EAAG,QAAO,CAAC,OAAO,IAAI,CAAC;AAC9C,UAAM,gBAAgB,IAAI,kBAAkB,SAAY,IAAI,gBAAgB,KAAK,QAAQ;AACzF,UAAM,eAAe,IAAI,iBAAiB,SAAY,IAAI,eAAe,KAAK,QAAQ;AACtF,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,KAAK,eAAe,KAAK,KAAK,SAAS,CAAC,GAAG,GAAG;AAClD,UAAM,YAAY,WAAW,WAAW,SAAS,CAAC;AAClD,QAAI,cAAc,IAAI,gBAAgB,SAAY,IAAI,cAAc,KAAK,QAAQ;AACjF,QAAI,gBAAgB,OAAW,eAAc;AAC7C,UAAM,MAAM,IAAI,OAAO,KAAK;AAC5B,UAAM,0BAA0B,IAAI,2BAA2B,KAAK,QAAQ;AAC5E,SAAI,2BAAK,mBAAkB,UAAU;AACnC,UAAI,yBAAyB;AAC3B,YAAI,eAAe;AACjB,iBAAO;AAAA,YACL,KAAK,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG;AAAA,YACrC,SAAS;AAAA,YACT,cAAc;AAAA,YACd,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,YAAY,KAAK,qBAAqB,GAAG;AAAA,UAC3C;AAAA,QACF;AACA,eAAO,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG;AAAA,MACzC;AACA,UAAI,eAAe;AACjB,eAAO;AAAA,UACL,KAAK;AAAA,UACL,SAAS;AAAA,UACT,cAAc;AAAA,UACd,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,YAAY,KAAK,qBAAqB,GAAG;AAAA,QAC3C;AAAA,MACF;AACA,aAAO;AAAA,IACT;AACA,UAAM,WAAW,KAAK,QAAQ,MAAM,GAAG;AACvC,QAAI,MAAM,qCAAU;AACpB,UAAM,cAAa,qCAAU,YAAW;AACxC,UAAM,mBAAkB,qCAAU,iBAAgB;AAClD,UAAM,WAAW,CAAC,mBAAmB,qBAAqB,iBAAiB;AAC3E,UAAM,aAAa,IAAI,eAAe,SAAY,IAAI,aAAa,KAAK,QAAQ;AAChF,UAAM,6BAA6B,CAAC,KAAK,cAAc,KAAK,WAAW;AACvE,UAAM,sBAAsB,IAAI,UAAU,UAAa,CAAC,SAAS,IAAI,KAAK;AAC1E,UAAM,kBAAkB,YAAW,gBAAgB,GAAG;AACtD,UAAM,qBAAqB,sBAAsB,KAAK,eAAe,UAAU,KAAK,IAAI,OAAO,GAAG,IAAI;AACtG,UAAM,oCAAoC,IAAI,WAAW,sBAAsB,KAAK,eAAe,UAAU,KAAK,IAAI,OAAO;AAAA,MAC3H,SAAS;AAAA,IACX,CAAC,IAAI;AACL,UAAM,wBAAwB,uBAAuB,CAAC,IAAI,WAAW,IAAI,UAAU;AACnF,UAAM,eAAe,yBAAyB,IAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,KAAK,IAAI,eAAe,kBAAkB,EAAE,KAAK,IAAI,eAAe,iCAAiC,EAAE,KAAK,IAAI;AACnN,QAAI,gBAAgB;AACpB,QAAI,8BAA8B,CAAC,OAAO,iBAAiB;AACzD,sBAAgB;AAAA,IAClB;AACA,UAAM,iBAAiB,qBAAqB,aAAa;AACzD,UAAM,UAAU,OAAO,UAAU,SAAS,MAAM,aAAa;AAC7D,QAAI,8BAA8B,iBAAiB,kBAAkB,SAAS,QAAQ,OAAO,IAAI,KAAK,EAAE,SAAS,UAAU,KAAK,MAAM,QAAQ,aAAa,IAAI;AAC7J,UAAI,CAAC,IAAI,iBAAiB,CAAC,KAAK,QAAQ,eAAe;AACrD,YAAI,CAAC,KAAK,QAAQ,uBAAuB;AACvC,eAAK,OAAO,KAAK,iEAAiE;AAAA,QACpF;AACA,cAAM,IAAI,KAAK,QAAQ,wBAAwB,KAAK,QAAQ,sBAAsB,YAAY,eAAe;AAAA,UAC3G,GAAG;AAAA,UACH,IAAI;AAAA,QACN,CAAC,IAAI,QAAQ,GAAG,KAAK,KAAK,QAAQ;AAClC,YAAI,eAAe;AACjB,mBAAS,MAAM;AACf,mBAAS,aAAa,KAAK,qBAAqB,GAAG;AACnD,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AACA,UAAI,cAAc;AAChB,cAAM,iBAAiB,MAAM,QAAQ,aAAa;AAClD,cAAMC,QAAO,iBAAiB,CAAC,IAAI,CAAC;AACpC,cAAM,cAAc,iBAAiB,kBAAkB;AACvD,mBAAW,KAAK,eAAe;AAC7B,cAAI,OAAO,UAAU,eAAe,KAAK,eAAe,CAAC,GAAG;AAC1D,kBAAM,UAAU,GAAG,WAAW,GAAG,YAAY,GAAG,CAAC;AACjD,gBAAI,mBAAmB,CAAC,KAAK;AAC3B,cAAAA,MAAK,CAAC,IAAI,KAAK,UAAU,SAAS;AAAA,gBAChC,GAAG;AAAA,gBACH,cAAc,qBAAqB,YAAY,IAAI,aAAa,CAAC,IAAI;AAAA,gBACrE,GAAG;AAAA,kBACD,YAAY;AAAA,kBACZ,IAAI;AAAA,gBACN;AAAA,cACF,CAAC;AAAA,YACH,OAAO;AACL,cAAAA,MAAK,CAAC,IAAI,KAAK,UAAU,SAAS;AAAA,gBAChC,GAAG;AAAA,gBACH,GAAG;AAAA,kBACD,YAAY;AAAA,kBACZ,IAAI;AAAA,gBACN;AAAA,cACF,CAAC;AAAA,YACH;AACA,gBAAIA,MAAK,CAAC,MAAM,QAAS,CAAAA,MAAK,CAAC,IAAI,cAAc,CAAC;AAAA,UACpD;AAAA,QACF;AACA,cAAMA;AAAA,MACR;AAAA,IACF,WAAW,8BAA8B,SAAS,UAAU,KAAK,MAAM,QAAQ,GAAG,GAAG;AACnF,YAAM,IAAI,KAAK,UAAU;AACzB,UAAI,IAAK,OAAM,KAAK,kBAAkB,KAAK,MAAM,KAAK,OAAO;AAAA,IAC/D,OAAO;AACL,UAAI,cAAc;AAClB,UAAI,UAAU;AACd,UAAI,CAAC,KAAK,cAAc,GAAG,KAAK,iBAAiB;AAC/C,sBAAc;AACd,cAAM;AAAA,MACR;AACA,UAAI,CAAC,KAAK,cAAc,GAAG,GAAG;AAC5B,kBAAU;AACV,cAAM;AAAA,MACR;AACA,YAAM,iCAAiC,IAAI,kCAAkC,KAAK,QAAQ;AAC1F,YAAM,gBAAgB,kCAAkC,UAAU,SAAY;AAC9E,YAAM,gBAAgB,mBAAmB,iBAAiB,OAAO,KAAK,QAAQ;AAC9E,UAAI,WAAW,eAAe,eAAe;AAC3C,aAAK,OAAO,IAAI,gBAAgB,cAAc,cAAc,KAAK,WAAW,KAAK,gBAAgB,eAAe,GAAG;AACnH,YAAI,cAAc;AAChB,gBAAM,KAAK,KAAK,QAAQ,KAAK;AAAA,YAC3B,GAAG;AAAA,YACH,cAAc;AAAA,UAChB,CAAC;AACD,cAAI,MAAM,GAAG,IAAK,MAAK,OAAO,KAAK,iLAAiL;AAAA,QACtN;AACA,YAAI,OAAO,CAAC;AACZ,cAAM,eAAe,KAAK,cAAc,iBAAiB,KAAK,QAAQ,aAAa,IAAI,OAAO,KAAK,QAAQ;AAC3G,YAAI,KAAK,QAAQ,kBAAkB,cAAc,gBAAgB,aAAa,CAAC,GAAG;AAChF,mBAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,iBAAK,KAAK,aAAa,CAAC,CAAC;AAAA,UAC3B;AAAA,QACF,WAAW,KAAK,QAAQ,kBAAkB,OAAO;AAC/C,iBAAO,KAAK,cAAc,mBAAmB,IAAI,OAAO,KAAK,QAAQ;AAAA,QACvE,OAAO;AACL,eAAK,KAAK,IAAI,OAAO,KAAK,QAAQ;AAAA,QACpC;AACA,cAAM,OAAO,CAAC,GAAG,GAAG,yBAAyB;AA5nBrD;AA6nBU,gBAAM,oBAAoB,mBAAmB,yBAAyB,MAAM,uBAAuB;AACnG,cAAI,KAAK,QAAQ,mBAAmB;AAClC,iBAAK,QAAQ,kBAAkB,GAAG,WAAW,GAAG,mBAAmB,eAAe,GAAG;AAAA,UACvF,YAAW,UAAK,qBAAL,mBAAuB,aAAa;AAC7C,iBAAK,iBAAiB,YAAY,GAAG,WAAW,GAAG,mBAAmB,eAAe,GAAG;AAAA,UAC1F;AACA,eAAK,KAAK,cAAc,GAAG,WAAW,GAAG,GAAG;AAAA,QAC9C;AACA,YAAI,KAAK,QAAQ,aAAa;AAC5B,cAAI,KAAK,QAAQ,sBAAsB,qBAAqB;AAC1D,iBAAK,QAAQ,cAAY;AACvB,oBAAM,WAAW,KAAK,eAAe,YAAY,UAAU,GAAG;AAC9D,kBAAI,yBAAyB,IAAI,eAAe,KAAK,QAAQ,eAAe,MAAM,KAAK,SAAS,QAAQ,GAAG,KAAK,QAAQ,eAAe,MAAM,IAAI,GAAG;AAClJ,yBAAS,KAAK,GAAG,KAAK,QAAQ,eAAe,MAAM;AAAA,cACrD;AACA,uBAAS,QAAQ,YAAU;AACzB,qBAAK,CAAC,QAAQ,GAAG,MAAM,QAAQ,IAAI,eAAe,MAAM,EAAE,KAAK,YAAY;AAAA,cAC7E,CAAC;AAAA,YACH,CAAC;AAAA,UACH,OAAO;AACL,iBAAK,MAAM,KAAK,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AACA,YAAM,KAAK,kBAAkB,KAAK,MAAM,KAAK,UAAU,OAAO;AAC9D,UAAI,WAAW,QAAQ,OAAO,KAAK,QAAQ,6BAA6B;AACtE,cAAM,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG;AAAA,MACxC;AACA,WAAK,WAAW,gBAAgB,KAAK,QAAQ,wBAAwB;AACnE,cAAM,KAAK,QAAQ,uBAAuB,KAAK,QAAQ,8BAA8B,GAAG,SAAS,GAAG,WAAW,GAAG,GAAG,KAAK,KAAK,cAAc,MAAM,QAAW,GAAG;AAAA,MACnK;AAAA,IACF;AACA,QAAI,eAAe;AACjB,eAAS,MAAM;AACf,eAAS,aAAa,KAAK,qBAAqB,GAAG;AACnD,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,KAAK,KAAK,KAAK,UAAU,SAAS;AApqBtD;AAqqBI,SAAI,UAAK,eAAL,mBAAiB,OAAO;AAC1B,YAAM,KAAK,WAAW,MAAM,KAAK;AAAA,QAC/B,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL,GAAG,IAAI,OAAO,KAAK,YAAY,SAAS,SAAS,SAAS,QAAQ,SAAS,SAAS;AAAA,QAClF;AAAA,MACF,CAAC;AAAA,IACH,WAAW,CAAC,IAAI,mBAAmB;AACjC,UAAI,IAAI,cAAe,MAAK,aAAa,KAAK;AAAA,QAC5C,GAAG;AAAA,QACH,GAAG;AAAA,UACD,eAAe;AAAA,YACb,GAAG,KAAK,QAAQ;AAAA,YAChB,GAAG,IAAI;AAAA,UACT;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,kBAAkB,SAAS,GAAG,QAAM,gCAAK,kBAAL,mBAAoB,qBAAoB,SAAY,IAAI,cAAc,kBAAkB,KAAK,QAAQ,cAAc;AAC7J,UAAI;AACJ,UAAI,iBAAiB;AACnB,cAAM,KAAK,IAAI,MAAM,KAAK,aAAa,aAAa;AACpD,kBAAU,MAAM,GAAG;AAAA,MACrB;AACA,UAAI,OAAO,IAAI,WAAW,CAAC,SAAS,IAAI,OAAO,IAAI,IAAI,UAAU;AACjE,UAAI,KAAK,QAAQ,cAAc,iBAAkB,QAAO;AAAA,QACtD,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL;AACA,YAAM,KAAK,aAAa,YAAY,KAAK,MAAM,IAAI,OAAO,KAAK,YAAY,SAAS,SAAS,GAAG;AAChG,UAAI,iBAAiB;AACnB,cAAM,KAAK,IAAI,MAAM,KAAK,aAAa,aAAa;AACpD,cAAM,UAAU,MAAM,GAAG;AACzB,YAAI,UAAU,QAAS,KAAI,OAAO;AAAA,MACpC;AACA,UAAI,CAAC,IAAI,OAAO,YAAY,SAAS,IAAK,KAAI,MAAM,KAAK,YAAY,SAAS;AAC9E,UAAI,IAAI,SAAS,MAAO,OAAM,KAAK,aAAa,KAAK,KAAK,IAAI,SAAS;AACrE,aAAI,mCAAU,QAAO,KAAK,CAAC,KAAK,CAAC,IAAI,SAAS;AAC5C,eAAK,OAAO,KAAK,6CAA6C,KAAK,CAAC,CAAC,YAAY,IAAI,CAAC,CAAC,EAAE;AACzF,iBAAO;AAAA,QACT;AACA,eAAO,KAAK,UAAU,GAAG,MAAM,GAAG;AAAA,MACpC,GAAG,GAAG;AACN,UAAI,IAAI,cAAe,MAAK,aAAa,MAAM;AAAA,IACjD;AACA,UAAM,cAAc,IAAI,eAAe,KAAK,QAAQ;AACpD,UAAM,qBAAqB,SAAS,WAAW,IAAI,CAAC,WAAW,IAAI;AACnE,QAAI,OAAO,SAAQ,yDAAoB,WAAU,IAAI,uBAAuB,OAAO;AACjF,YAAM,cAAc,OAAO,oBAAoB,KAAK,KAAK,KAAK,WAAW,KAAK,QAAQ,0BAA0B;AAAA,QAC9G,cAAc;AAAA,UACZ,GAAG;AAAA,UACH,YAAY,KAAK,qBAAqB,GAAG;AAAA,QAC3C;AAAA,QACA,GAAG;AAAA,MACL,IAAI,KAAK,IAAI;AAAA,IACf;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,MAAM,MAAM,CAAC,GAAG;AACtB,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,QAAI,SAAS,IAAI,EAAG,QAAO,CAAC,IAAI;AAChC,SAAK,QAAQ,OAAK;AAChB,UAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,YAAM,YAAY,KAAK,eAAe,GAAG,GAAG;AAC5C,YAAM,MAAM,UAAU;AACtB,gBAAU;AACV,UAAI,aAAa,UAAU;AAC3B,UAAI,KAAK,QAAQ,WAAY,cAAa,WAAW,OAAO,KAAK,QAAQ,UAAU;AACnF,YAAM,sBAAsB,IAAI,UAAU,UAAa,CAAC,SAAS,IAAI,KAAK;AAC1E,YAAM,wBAAwB,uBAAuB,CAAC,IAAI,WAAW,IAAI,UAAU;AACnF,YAAM,uBAAuB,IAAI,YAAY,WAAc,SAAS,IAAI,OAAO,KAAK,OAAO,IAAI,YAAY,aAAa,IAAI,YAAY;AACxI,YAAM,QAAQ,IAAI,OAAO,IAAI,OAAO,KAAK,cAAc,mBAAmB,IAAI,OAAO,KAAK,UAAU,IAAI,WAAW;AACnH,iBAAW,QAAQ,QAAM;AAhvB/B;AAivBQ,YAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,iBAAS;AACT,YAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,OAAK,UAAK,UAAL,mBAAY,uBAAsB,GAAC,UAAK,UAAL,mBAAY,mBAAmB,UAAS;AACvH,2BAAiB,GAAG,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,IAAI;AACxC,eAAK,OAAO,KAAK,QAAQ,OAAO,oBAAoB,MAAM,KAAK,IAAI,CAAC,sCAAsC,MAAM,wBAAwB,0NAA0N;AAAA,QACpW;AACA,cAAM,QAAQ,UAAQ;AAvvB9B,cAAAC;AAwvBU,cAAI,KAAK,cAAc,KAAK,EAAG;AAC/B,oBAAU;AACV,gBAAM,YAAY,CAAC,GAAG;AACtB,eAAIA,MAAA,KAAK,eAAL,gBAAAA,IAAiB,eAAe;AAClC,iBAAK,WAAW,cAAc,WAAW,KAAK,MAAM,IAAI,GAAG;AAAA,UAC7D,OAAO;AACL,gBAAI;AACJ,gBAAI,oBAAqB,gBAAe,KAAK,eAAe,UAAU,MAAM,IAAI,OAAO,GAAG;AAC1F,kBAAM,aAAa,GAAG,KAAK,QAAQ,eAAe;AAClD,kBAAM,gBAAgB,GAAG,KAAK,QAAQ,eAAe,UAAU,KAAK,QAAQ,eAAe;AAC3F,gBAAI,qBAAqB;AACvB,wBAAU,KAAK,MAAM,YAAY;AACjC,kBAAI,IAAI,WAAW,aAAa,QAAQ,aAAa,MAAM,GAAG;AAC5D,0BAAU,KAAK,MAAM,aAAa,QAAQ,eAAe,KAAK,QAAQ,eAAe,CAAC;AAAA,cACxF;AACA,kBAAI,uBAAuB;AACzB,0BAAU,KAAK,MAAM,UAAU;AAAA,cACjC;AAAA,YACF;AACA,gBAAI,sBAAsB;AACxB,oBAAM,aAAa,GAAG,GAAG,GAAG,KAAK,QAAQ,gBAAgB,GAAG,IAAI,OAAO;AACvE,wBAAU,KAAK,UAAU;AACzB,kBAAI,qBAAqB;AACvB,0BAAU,KAAK,aAAa,YAAY;AACxC,oBAAI,IAAI,WAAW,aAAa,QAAQ,aAAa,MAAM,GAAG;AAC5D,4BAAU,KAAK,aAAa,aAAa,QAAQ,eAAe,KAAK,QAAQ,eAAe,CAAC;AAAA,gBAC/F;AACA,oBAAI,uBAAuB;AACzB,4BAAU,KAAK,aAAa,UAAU;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI;AACJ,iBAAO,cAAc,UAAU,IAAI,GAAG;AACpC,gBAAI,CAAC,KAAK,cAAc,KAAK,GAAG;AAC9B,6BAAe;AACf,sBAAQ,KAAK,YAAY,MAAM,IAAI,aAAa,GAAG;AAAA,YACrD;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AACD,WAAO;AAAA,MACL,KAAK;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA,EACA,cAAc,KAAK;AACjB,WAAO,QAAQ,UAAa,EAAE,CAAC,KAAK,QAAQ,cAAc,QAAQ,SAAS,EAAE,CAAC,KAAK,QAAQ,qBAAqB,QAAQ;AAAA,EAC1H;AAAA,EACA,YAAY,MAAM,IAAI,KAAKF,WAAU,CAAC,GAAG;AA9yB3C;AA+yBI,SAAI,UAAK,eAAL,mBAAiB,YAAa,QAAO,KAAK,WAAW,YAAY,MAAM,IAAI,KAAKA,QAAO;AAC3F,WAAO,KAAK,cAAc,YAAY,MAAM,IAAI,KAAKA,QAAO;AAAA,EAC9D;AAAA,EACA,qBAAqBA,WAAU,CAAC,GAAG;AACjC,UAAM,cAAc,CAAC,gBAAgB,WAAW,WAAW,WAAW,OAAO,QAAQ,eAAe,MAAM,gBAAgB,eAAe,iBAAiB,iBAAiB,cAAc,eAAe,eAAe;AACvN,UAAM,2BAA2BA,SAAQ,WAAW,CAAC,SAASA,SAAQ,OAAO;AAC7E,QAAI,OAAO,2BAA2BA,SAAQ,UAAUA;AACxD,QAAI,4BAA4B,OAAOA,SAAQ,UAAU,aAAa;AACpE,WAAK,QAAQA,SAAQ;AAAA,IACvB;AACA,QAAI,KAAK,QAAQ,cAAc,kBAAkB;AAC/C,aAAO;AAAA,QACL,GAAG,KAAK,QAAQ,cAAc;AAAA,QAC9B,GAAG;AAAA,MACL;AAAA,IACF;AACA,QAAI,CAAC,0BAA0B;AAC7B,aAAO;AAAA,QACL,GAAG;AAAA,MACL;AACA,iBAAW,OAAO,aAAa;AAC7B,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,gBAAgBA,UAAS;AAC9B,UAAM,SAAS;AACf,eAAW,UAAUA,UAAS;AAC5B,UAAI,OAAO,UAAU,eAAe,KAAKA,UAAS,MAAM,KAAK,WAAW,OAAO,UAAU,GAAG,OAAO,MAAM,KAAK,WAAcA,SAAQ,MAAM,GAAG;AAC3I,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAYA,UAAS;AACnB,SAAK,UAAUA;AACf,SAAK,gBAAgB,KAAK,QAAQ,iBAAiB;AACnD,SAAK,SAAS,WAAW,OAAO,eAAe;AAAA,EACjD;AAAA,EACA,sBAAsB,MAAM;AAC1B,WAAO,eAAe,IAAI;AAC1B,QAAI,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,EAAG,QAAO;AAC3C,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,QAAI,EAAE,WAAW,EAAG,QAAO;AAC3B,MAAE,IAAI;AACN,QAAI,EAAE,EAAE,SAAS,CAAC,EAAE,YAAY,MAAM,IAAK,QAAO;AAClD,WAAO,KAAK,mBAAmB,EAAE,KAAK,GAAG,CAAC;AAAA,EAC5C;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAO,eAAe,IAAI;AAC1B,QAAI,CAAC,QAAQ,KAAK,QAAQ,GAAG,IAAI,EAAG,QAAO;AAC3C,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,WAAO,KAAK,mBAAmB,EAAE,CAAC,CAAC;AAAA,EACrC;AAAA,EACA,mBAAmB,MAAM;AACvB,QAAI,SAAS,IAAI,KAAK,KAAK,QAAQ,GAAG,IAAI,IAAI;AAC5C,UAAI;AACJ,UAAI;AACF,wBAAgB,KAAK,oBAAoB,IAAI,EAAE,CAAC;AAAA,MAClD,SAAS,GAAG;AAAA,MAAC;AACb,UAAI,iBAAiB,KAAK,QAAQ,cAAc;AAC9C,wBAAgB,cAAc,YAAY;AAAA,MAC5C;AACA,UAAI,cAAe,QAAO;AAC1B,UAAI,KAAK,QAAQ,cAAc;AAC7B,eAAO,KAAK,YAAY;AAAA,MAC1B;AACA,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,eAAe,KAAK,YAAY,IAAI;AAAA,EACpF;AAAA,EACA,gBAAgB,MAAM;AACpB,QAAI,KAAK,QAAQ,SAAS,kBAAkB,KAAK,QAAQ,0BAA0B;AACjF,aAAO,KAAK,wBAAwB,IAAI;AAAA,IAC1C;AACA,WAAO,CAAC,KAAK,iBAAiB,CAAC,KAAK,cAAc,UAAU,KAAK,cAAc,QAAQ,IAAI,IAAI;AAAA,EACjG;AAAA,EACA,sBAAsB,OAAO;AAC3B,QAAI,CAAC,MAAO,QAAO;AACnB,QAAI;AACJ,UAAM,QAAQ,UAAQ;AACpB,UAAI,MAAO;AACX,YAAM,aAAa,KAAK,mBAAmB,IAAI;AAC/C,UAAI,CAAC,KAAK,QAAQ,iBAAiB,KAAK,gBAAgB,UAAU,EAAG,SAAQ;AAAA,IAC/E,CAAC;AACD,QAAI,CAAC,SAAS,KAAK,QAAQ,eAAe;AACxC,YAAM,QAAQ,UAAQ;AACpB,YAAI,MAAO;AACX,cAAM,YAAY,KAAK,sBAAsB,IAAI;AACjD,YAAI,KAAK,gBAAgB,SAAS,EAAG,QAAO,QAAQ;AACpD,cAAM,UAAU,KAAK,wBAAwB,IAAI;AACjD,YAAI,KAAK,gBAAgB,OAAO,EAAG,QAAO,QAAQ;AAClD,gBAAQ,KAAK,QAAQ,cAAc,KAAK,kBAAgB;AACtD,cAAI,iBAAiB,QAAS,QAAO;AACrC,cAAI,aAAa,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,EAAG;AAC/D,cAAI,aAAa,QAAQ,GAAG,IAAI,KAAK,QAAQ,QAAQ,GAAG,IAAI,KAAK,aAAa,UAAU,GAAG,aAAa,QAAQ,GAAG,CAAC,MAAM,QAAS,QAAO;AAC1I,cAAI,aAAa,QAAQ,OAAO,MAAM,KAAK,QAAQ,SAAS,EAAG,QAAO;AAAA,QACxE,CAAC;AAAA,MACH,CAAC;AAAA,IACH;AACA,QAAI,CAAC,MAAO,SAAQ,KAAK,iBAAiB,KAAK,QAAQ,WAAW,EAAE,CAAC;AACrE,WAAO;AAAA,EACT;AAAA,EACA,iBAAiB,WAAW,MAAM;AAChC,QAAI,CAAC,UAAW,QAAO,CAAC;AACxB,QAAI,OAAO,cAAc,WAAY,aAAY,UAAU,IAAI;AAC/D,QAAI,SAAS,SAAS,EAAG,aAAY,CAAC,SAAS;AAC/C,QAAI,MAAM,QAAQ,SAAS,EAAG,QAAO;AACrC,QAAI,CAAC,KAAM,QAAO,UAAU,WAAW,CAAC;AACxC,QAAI,QAAQ,UAAU,IAAI;AAC1B,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,sBAAsB,IAAI,CAAC;AAC9D,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,mBAAmB,IAAI,CAAC;AAC3D,QAAI,CAAC,MAAO,SAAQ,UAAU,KAAK,wBAAwB,IAAI,CAAC;AAChE,QAAI,CAAC,MAAO,SAAQ,UAAU;AAC9B,WAAO,SAAS,CAAC;AAAA,EACnB;AAAA,EACA,mBAAmB,MAAM,cAAc;AACrC,UAAM,gBAAgB,KAAK,iBAAiB,gBAAgB,KAAK,QAAQ,eAAe,CAAC,GAAG,IAAI;AAChG,UAAM,QAAQ,CAAC;AACf,UAAM,UAAU,OAAK;AACnB,UAAI,CAAC,EAAG;AACR,UAAI,KAAK,gBAAgB,CAAC,GAAG;AAC3B,cAAM,KAAK,CAAC;AAAA,MACd,OAAO;AACL,aAAK,OAAO,KAAK,uDAAuD,CAAC,EAAE;AAAA,MAC7E;AAAA,IACF;AACA,QAAI,SAAS,IAAI,MAAM,KAAK,QAAQ,GAAG,IAAI,MAAM,KAAK,QAAQ,GAAG,IAAI,KAAK;AACxE,UAAI,KAAK,QAAQ,SAAS,eAAgB,SAAQ,KAAK,mBAAmB,IAAI,CAAC;AAC/E,UAAI,KAAK,QAAQ,SAAS,kBAAkB,KAAK,QAAQ,SAAS,cAAe,SAAQ,KAAK,sBAAsB,IAAI,CAAC;AACzH,UAAI,KAAK,QAAQ,SAAS,cAAe,SAAQ,KAAK,wBAAwB,IAAI,CAAC;AAAA,IACrF,WAAW,SAAS,IAAI,GAAG;AACzB,cAAQ,KAAK,mBAAmB,IAAI,CAAC;AAAA,IACvC;AACA,kBAAc,QAAQ,QAAM;AAC1B,UAAI,MAAM,QAAQ,EAAE,IAAI,EAAG,SAAQ,KAAK,mBAAmB,EAAE,CAAC;AAAA,IAChE,CAAC;AACD,WAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AAAA,EACN,OAAO;AACT;AACA,IAAM,YAAY;AAAA,EAChB,QAAQ,WAAS,UAAU,IAAI,QAAQ;AAAA,EACvC,iBAAiB,OAAO;AAAA,IACtB,kBAAkB,CAAC,OAAO,OAAO;AAAA,EACnC;AACF;AACA,IAAM,iBAAN,MAAqB;AAAA,EACnB,YAAY,eAAeA,WAAU,CAAC,GAAG;AACvC,SAAK,gBAAgB;AACrB,SAAK,UAAUA;AACf,SAAK,SAAS,WAAW,OAAO,gBAAgB;AAChD,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,KAAK,KAAK;AAChB,SAAK,MAAM,GAAG,IAAI;AAAA,EACpB;AAAA,EACA,aAAa;AACX,SAAK,mBAAmB,CAAC;AAAA,EAC3B;AAAA,EACA,QAAQ,MAAMA,WAAU,CAAC,GAAG;AAC1B,UAAM,cAAc,eAAe,SAAS,QAAQ,OAAO,IAAI;AAC/D,UAAM,OAAOA,SAAQ,UAAU,YAAY;AAC3C,UAAM,WAAW,KAAK,UAAU;AAAA,MAC9B;AAAA,MACA;AAAA,IACF,CAAC;AACD,QAAI,YAAY,KAAK,kBAAkB;AACrC,aAAO,KAAK,iBAAiB,QAAQ;AAAA,IACvC;AACA,QAAI;AACJ,QAAI;AACF,aAAO,IAAI,KAAK,YAAY,aAAa;AAAA,QACvC;AAAA,MACF,CAAC;AAAA,IACH,SAAS,KAAK;AACZ,UAAI,CAAC,MAAM;AACT,aAAK,OAAO,MAAM,+CAA+C;AACjE,eAAO;AAAA,MACT;AACA,UAAI,CAAC,KAAK,MAAM,KAAK,EAAG,QAAO;AAC/B,YAAM,UAAU,KAAK,cAAc,wBAAwB,IAAI;AAC/D,aAAO,KAAK,QAAQ,SAASA,QAAO;AAAA,IACtC;AACA,SAAK,iBAAiB,QAAQ,IAAI;AAClC,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAMA,WAAU,CAAC,GAAG;AAC9B,QAAI,OAAO,KAAK,QAAQ,MAAMA,QAAO;AACrC,QAAI,CAAC,KAAM,QAAO,KAAK,QAAQ,OAAOA,QAAO;AAC7C,YAAO,6BAAM,kBAAkB,iBAAiB,UAAS;AAAA,EAC3D;AAAA,EACA,oBAAoB,MAAM,KAAKA,WAAU,CAAC,GAAG;AAC3C,WAAO,KAAK,YAAY,MAAMA,QAAO,EAAE,IAAI,YAAU,GAAG,GAAG,GAAG,MAAM,EAAE;AAAA,EACxE;AAAA,EACA,YAAY,MAAMA,WAAU,CAAC,GAAG;AAC9B,QAAI,OAAO,KAAK,QAAQ,MAAMA,QAAO;AACrC,QAAI,CAAC,KAAM,QAAO,KAAK,QAAQ,OAAOA,QAAO;AAC7C,QAAI,CAAC,KAAM,QAAO,CAAC;AACnB,WAAO,KAAK,gBAAgB,EAAE,iBAAiB,KAAK,CAAC,iBAAiB,oBAAoB,cAAc,eAAe,IAAI,cAAc,eAAe,CAAC,EAAE,IAAI,oBAAkB,GAAG,KAAK,QAAQ,OAAO,GAAGA,SAAQ,UAAU,UAAU,KAAK,QAAQ,OAAO,KAAK,EAAE,GAAG,cAAc,EAAE;AAAA,EACvR;AAAA,EACA,UAAU,MAAM,OAAOA,WAAU,CAAC,GAAG;AACnC,UAAM,OAAO,KAAK,QAAQ,MAAMA,QAAO;AACvC,QAAI,MAAM;AACR,aAAO,GAAG,KAAK,QAAQ,OAAO,GAAGA,SAAQ,UAAU,UAAU,KAAK,QAAQ,OAAO,KAAK,EAAE,GAAG,KAAK,OAAO,KAAK,CAAC;AAAA,IAC/G;AACA,SAAK,OAAO,KAAK,6BAA6B,IAAI,EAAE;AACpD,WAAO,KAAK,UAAU,OAAO,OAAOA,QAAO;AAAA,EAC7C;AACF;AAEA,IAAM,uBAAuB,CAAC,MAAM,aAAa,KAAK,eAAe,KAAK,sBAAsB,SAAS;AACvG,MAAI,OAAO,oBAAoB,MAAM,aAAa,GAAG;AACrD,MAAI,CAAC,QAAQ,uBAAuB,SAAS,GAAG,GAAG;AACjD,WAAO,SAAS,MAAM,KAAK,YAAY;AACvC,QAAI,SAAS,OAAW,QAAO,SAAS,aAAa,KAAK,YAAY;AAAA,EACxE;AACA,SAAO;AACT;AACA,IAAM,YAAY,SAAO,IAAI,QAAQ,OAAO,MAAM;AAClD,IAAM,eAAN,MAAmB;AAAA,EACjB,YAAYA,WAAU,CAAC,GAAG;AAxhC5B;AAyhCI,SAAK,SAAS,WAAW,OAAO,cAAc;AAC9C,SAAK,UAAUA;AACf,SAAK,WAAS,KAAAA,YAAA,gBAAAA,SAAS,kBAAT,mBAAwB,YAAW,WAAS;AAC1D,SAAK,KAAKA,QAAO;AAAA,EACnB;AAAA,EACA,KAAKA,WAAU,CAAC,GAAG;AACjB,QAAI,CAACA,SAAQ,cAAe,CAAAA,SAAQ,gBAAgB;AAAA,MAClD,aAAa;AAAA,IACf;AACA,UAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAIA,SAAQ;AACZ,SAAK,SAAS,aAAa,SAAY,WAAW;AAClD,SAAK,cAAc,gBAAgB,SAAY,cAAc;AAC7D,SAAK,sBAAsB,wBAAwB,SAAY,sBAAsB;AACrF,SAAK,SAAS,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAC9D,SAAK,SAAS,SAAS,YAAY,MAAM,IAAI,iBAAiB;AAC9D,SAAK,kBAAkB,mBAAmB;AAC1C,SAAK,iBAAiB,iBAAiB,KAAK,kBAAkB;AAC9D,SAAK,iBAAiB,KAAK,iBAAiB,KAAK,kBAAkB;AACnE,SAAK,gBAAgB,gBAAgB,YAAY,aAAa,IAAI,wBAAwB,YAAY,KAAK;AAC3G,SAAK,gBAAgB,gBAAgB,YAAY,aAAa,IAAI,wBAAwB,YAAY,GAAG;AACzG,SAAK,0BAA0B,2BAA2B;AAC1D,SAAK,cAAc,eAAe;AAClC,SAAK,eAAe,iBAAiB,SAAY,eAAe;AAChE,SAAK,YAAY;AAAA,EACnB;AAAA,EACA,QAAQ;AACN,QAAI,KAAK,QAAS,MAAK,KAAK,KAAK,OAAO;AAAA,EAC1C;AAAA,EACA,cAAc;AACZ,UAAM,mBAAmB,CAAC,gBAAgB,YAAY;AACpD,WAAI,iDAAgB,YAAW,SAAS;AACtC,uBAAe,YAAY;AAC3B,eAAO;AAAA,MACT;AACA,aAAO,IAAI,OAAO,SAAS,GAAG;AAAA,IAChC;AACA,SAAK,SAAS,iBAAiB,KAAK,QAAQ,GAAG,KAAK,MAAM,QAAQ,KAAK,MAAM,EAAE;AAC/E,SAAK,iBAAiB,iBAAiB,KAAK,gBAAgB,GAAG,KAAK,MAAM,GAAG,KAAK,cAAc,QAAQ,KAAK,cAAc,GAAG,KAAK,MAAM,EAAE;AAC3I,SAAK,gBAAgB,iBAAiB,KAAK,eAAe,GAAG,KAAK,aAAa,QAAQ,KAAK,aAAa,EAAE;AAAA,EAC7G;AAAA,EACA,YAAY,KAAK,MAAM,KAAKA,UAAS;AAnlCvC;AAolCI,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,cAAc,KAAK,WAAW,KAAK,QAAQ,iBAAiB,KAAK,QAAQ,cAAc,oBAAoB,CAAC;AAClH,UAAM,eAAe,SAAO;AAC1B,UAAI,IAAI,QAAQ,KAAK,eAAe,IAAI,GAAG;AACzC,cAAM,OAAO,qBAAqB,MAAM,aAAa,KAAK,KAAK,QAAQ,cAAc,KAAK,QAAQ,mBAAmB;AACrH,eAAO,KAAK,eAAe,KAAK,OAAO,MAAM,QAAW,KAAK;AAAA,UAC3D,GAAGA;AAAA,UACH,GAAG;AAAA,UACH,kBAAkB;AAAA,QACpB,CAAC,IAAI;AAAA,MACP;AACA,YAAM,IAAI,IAAI,MAAM,KAAK,eAAe;AACxC,YAAM,IAAI,EAAE,MAAM,EAAE,KAAK;AACzB,YAAM,IAAI,EAAE,KAAK,KAAK,eAAe,EAAE,KAAK;AAC5C,aAAO,KAAK,OAAO,qBAAqB,MAAM,aAAa,GAAG,KAAK,QAAQ,cAAc,KAAK,QAAQ,mBAAmB,GAAG,GAAG,KAAK;AAAA,QAClI,GAAGA;AAAA,QACH,GAAG;AAAA,QACH,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,SAAK,YAAY;AACjB,UAAM,+BAA8BA,YAAA,gBAAAA,SAAS,gCAA+B,KAAK,QAAQ;AACzF,UAAM,oBAAkB,KAAAA,YAAA,gBAAAA,SAAS,kBAAT,mBAAwB,qBAAoB,SAAYA,SAAQ,cAAc,kBAAkB,KAAK,QAAQ,cAAc;AACnJ,UAAM,QAAQ,CAAC;AAAA,MACb,OAAO,KAAK;AAAA,MACZ,WAAW,SAAO,UAAU,GAAG;AAAA,IACjC,GAAG;AAAA,MACD,OAAO,KAAK;AAAA,MACZ,WAAW,SAAO,KAAK,cAAc,UAAU,KAAK,OAAO,GAAG,CAAC,IAAI,UAAU,GAAG;AAAA,IAClF,CAAC;AACD,UAAM,QAAQ,UAAQ;AACpB,iBAAW;AACX,aAAO,QAAQ,KAAK,MAAM,KAAK,GAAG,GAAG;AACnC,cAAM,aAAa,MAAM,CAAC,EAAE,KAAK;AACjC,gBAAQ,aAAa,UAAU;AAC/B,YAAI,UAAU,QAAW;AACvB,cAAI,OAAO,gCAAgC,YAAY;AACrD,kBAAM,OAAO,4BAA4B,KAAK,OAAOA,QAAO;AAC5D,oBAAQ,SAAS,IAAI,IAAI,OAAO;AAAA,UAClC,WAAWA,YAAW,OAAO,UAAU,eAAe,KAAKA,UAAS,UAAU,GAAG;AAC/E,oBAAQ;AAAA,UACV,WAAW,iBAAiB;AAC1B,oBAAQ,MAAM,CAAC;AACf;AAAA,UACF,OAAO;AACL,iBAAK,OAAO,KAAK,8BAA8B,UAAU,sBAAsB,GAAG,EAAE;AACpF,oBAAQ;AAAA,UACV;AAAA,QACF,WAAW,CAAC,SAAS,KAAK,KAAK,CAAC,KAAK,qBAAqB;AACxD,kBAAQ,WAAW,KAAK;AAAA,QAC1B;AACA,cAAM,YAAY,KAAK,UAAU,KAAK;AACtC,cAAM,IAAI,QAAQ,MAAM,CAAC,GAAG,SAAS;AACrC,YAAI,iBAAiB;AACnB,eAAK,MAAM,aAAa,MAAM;AAC9B,eAAK,MAAM,aAAa,MAAM,CAAC,EAAE;AAAA,QACnC,OAAO;AACL,eAAK,MAAM,YAAY;AAAA,QACzB;AACA;AACA,YAAI,YAAY,KAAK,aAAa;AAChC;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK,KAAK,IAAIA,WAAU,CAAC,GAAG;AAC1B,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,UAAM,mBAAmB,CAAC,KAAK,qBAAqB;AAClD,YAAM,MAAM,KAAK;AACjB,UAAI,IAAI,QAAQ,GAAG,IAAI,EAAG,QAAO;AACjC,YAAM,IAAI,IAAI,MAAM,IAAI,OAAO,GAAG,GAAG,OAAO,CAAC;AAC7C,UAAI,gBAAgB,IAAI,EAAE,CAAC,CAAC;AAC5B,YAAM,EAAE,CAAC;AACT,sBAAgB,KAAK,YAAY,eAAe,aAAa;AAC7D,YAAM,sBAAsB,cAAc,MAAM,IAAI;AACpD,YAAM,sBAAsB,cAAc,MAAM,IAAI;AACpD,YAAK,2DAAqB,WAAU,KAAK,MAAM,KAAK,CAAC,uBAAuB,oBAAoB,SAAS,MAAM,GAAG;AAChH,wBAAgB,cAAc,QAAQ,MAAM,GAAG;AAAA,MACjD;AACA,UAAI;AACF,wBAAgB,KAAK,MAAM,aAAa;AACxC,YAAI,iBAAkB,iBAAgB;AAAA,UACpC,GAAG;AAAA,UACH,GAAG;AAAA,QACL;AAAA,MACF,SAAS,GAAG;AACV,aAAK,OAAO,KAAK,oDAAoD,GAAG,IAAI,CAAC;AAC7E,eAAO,GAAG,GAAG,GAAG,GAAG,GAAG,aAAa;AAAA,MACrC;AACA,UAAI,cAAc,gBAAgB,cAAc,aAAa,QAAQ,KAAK,MAAM,IAAI,GAAI,QAAO,cAAc;AAC7G,aAAO;AAAA,IACT;AACA,WAAO,QAAQ,KAAK,cAAc,KAAK,GAAG,GAAG;AAC3C,UAAI,aAAa,CAAC;AAClB,sBAAgB;AAAA,QACd,GAAGA;AAAA,MACL;AACA,sBAAgB,cAAc,WAAW,CAAC,SAAS,cAAc,OAAO,IAAI,cAAc,UAAU;AACpG,oBAAc,qBAAqB;AACnC,aAAO,cAAc;AACrB,UAAI,WAAW;AACf,UAAI,MAAM,CAAC,EAAE,QAAQ,KAAK,eAAe,MAAM,MAAM,CAAC,OAAO,KAAK,MAAM,CAAC,CAAC,GAAG;AAC3E,cAAM,IAAI,MAAM,CAAC,EAAE,MAAM,KAAK,eAAe,EAAE,IAAI,UAAQ,KAAK,KAAK,CAAC;AACtE,cAAM,CAAC,IAAI,EAAE,MAAM;AACnB,qBAAa;AACb,mBAAW;AAAA,MACb;AACA,cAAQ,GAAG,iBAAiB,KAAK,MAAM,MAAM,CAAC,EAAE,KAAK,GAAG,aAAa,GAAG,aAAa;AACrF,UAAI,SAAS,MAAM,CAAC,MAAM,OAAO,CAAC,SAAS,KAAK,EAAG,QAAO;AAC1D,UAAI,CAAC,SAAS,KAAK,EAAG,SAAQ,WAAW,KAAK;AAC9C,UAAI,CAAC,OAAO;AACV,aAAK,OAAO,KAAK,qBAAqB,MAAM,CAAC,CAAC,gBAAgB,GAAG,EAAE;AACnE,gBAAQ;AAAA,MACV;AACA,UAAI,UAAU;AACZ,gBAAQ,WAAW,OAAO,CAAC,GAAG,MAAM,KAAK,OAAO,GAAG,GAAGA,SAAQ,KAAK;AAAA,UACjE,GAAGA;AAAA,UACH,kBAAkB,MAAM,CAAC,EAAE,KAAK;AAAA,QAClC,CAAC,GAAG,MAAM,KAAK,CAAC;AAAA,MAClB;AACA,YAAM,IAAI,QAAQ,MAAM,CAAC,GAAG,KAAK;AACjC,WAAK,OAAO,YAAY;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACF;AAEA,IAAM,iBAAiB,eAAa;AAClC,MAAI,aAAa,UAAU,YAAY,EAAE,KAAK;AAC9C,QAAM,gBAAgB,CAAC;AACvB,MAAI,UAAU,QAAQ,GAAG,IAAI,IAAI;AAC/B,UAAM,IAAI,UAAU,MAAM,GAAG;AAC7B,iBAAa,EAAE,CAAC,EAAE,YAAY,EAAE,KAAK;AACrC,UAAM,SAAS,EAAE,CAAC,EAAE,UAAU,GAAG,EAAE,CAAC,EAAE,SAAS,CAAC;AAChD,QAAI,eAAe,cAAc,OAAO,QAAQ,GAAG,IAAI,GAAG;AACxD,UAAI,CAAC,cAAc,SAAU,eAAc,WAAW,OAAO,KAAK;AAAA,IACpE,WAAW,eAAe,kBAAkB,OAAO,QAAQ,GAAG,IAAI,GAAG;AACnE,UAAI,CAAC,cAAc,MAAO,eAAc,QAAQ,OAAO,KAAK;AAAA,IAC9D,OAAO;AACL,YAAM,OAAO,OAAO,MAAM,GAAG;AAC7B,WAAK,QAAQ,SAAO;AAClB,YAAI,KAAK;AACP,gBAAM,CAAC,KAAK,GAAG,IAAI,IAAI,IAAI,MAAM,GAAG;AACpC,gBAAM,MAAM,KAAK,KAAK,GAAG,EAAE,KAAK,EAAE,QAAQ,YAAY,EAAE;AACxD,gBAAM,aAAa,IAAI,KAAK;AAC5B,cAAI,CAAC,cAAc,UAAU,EAAG,eAAc,UAAU,IAAI;AAC5D,cAAI,QAAQ,QAAS,eAAc,UAAU,IAAI;AACjD,cAAI,QAAQ,OAAQ,eAAc,UAAU,IAAI;AAChD,cAAI,CAAC,MAAM,GAAG,EAAG,eAAc,UAAU,IAAI,SAAS,KAAK,EAAE;AAAA,QAC/D;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAM,wBAAwB,QAAM;AAClC,QAAM,QAAQ,CAAC;AACf,SAAO,CAAC,GAAG,GAAG,MAAM;AAClB,QAAI,cAAc;AAClB,QAAI,KAAK,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,aAAa,EAAE,gBAAgB,KAAK,EAAE,EAAE,gBAAgB,GAAG;AAC5G,oBAAc;AAAA,QACZ,GAAG;AAAA,QACH,CAAC,EAAE,gBAAgB,GAAG;AAAA,MACxB;AAAA,IACF;AACA,UAAM,MAAM,IAAI,KAAK,UAAU,WAAW;AAC1C,QAAI,MAAM,MAAM,GAAG;AACnB,QAAI,CAAC,KAAK;AACR,YAAM,GAAG,eAAe,CAAC,GAAG,CAAC;AAC7B,YAAM,GAAG,IAAI;AAAA,IACf;AACA,WAAO,IAAI,CAAC;AAAA,EACd;AACF;AACA,IAAM,2BAA2B,QAAM,CAAC,GAAG,GAAG,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,EAAE,CAAC;AAC9E,IAAM,YAAN,MAAgB;AAAA,EACd,YAAYA,WAAU,CAAC,GAAG;AACxB,SAAK,SAAS,WAAW,OAAO,WAAW;AAC3C,SAAK,UAAUA;AACf,SAAK,KAAKA,QAAO;AAAA,EACnB;AAAA,EACA,KAAK,UAAUA,WAAU;AAAA,IACvB,eAAe,CAAC;AAAA,EAClB,GAAG;AACD,SAAK,kBAAkBA,SAAQ,cAAc,mBAAmB;AAChE,UAAM,KAAKA,SAAQ,sBAAsB,wBAAwB;AACjE,SAAK,UAAU;AAAA,MACb,QAAQ,GAAG,CAAC,KAAK,QAAQ;AACvB,cAAM,YAAY,IAAI,KAAK,aAAa,KAAK;AAAA,UAC3C,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,UAAU,GAAG,CAAC,KAAK,QAAQ;AACzB,cAAM,YAAY,IAAI,KAAK,aAAa,KAAK;AAAA,UAC3C,GAAG;AAAA,UACH,OAAO;AAAA,QACT,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,UAAU,GAAG,CAAC,KAAK,QAAQ;AACzB,cAAM,YAAY,IAAI,KAAK,eAAe,KAAK;AAAA,UAC7C,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,MACD,cAAc,GAAG,CAAC,KAAK,QAAQ;AAC7B,cAAM,YAAY,IAAI,KAAK,mBAAmB,KAAK;AAAA,UACjD,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,KAAK,IAAI,SAAS,KAAK;AAAA,MACxD,CAAC;AAAA,MACD,MAAM,GAAG,CAAC,KAAK,QAAQ;AACrB,cAAM,YAAY,IAAI,KAAK,WAAW,KAAK;AAAA,UACzC,GAAG;AAAA,QACL,CAAC;AACD,eAAO,SAAO,UAAU,OAAO,GAAG;AAAA,MACpC,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,IAAI,MAAM,IAAI;AACZ,SAAK,QAAQ,KAAK,YAAY,EAAE,KAAK,CAAC,IAAI;AAAA,EAC5C;AAAA,EACA,UAAU,MAAM,IAAI;AAClB,SAAK,QAAQ,KAAK,YAAY,EAAE,KAAK,CAAC,IAAI,sBAAsB,EAAE;AAAA,EACpE;AAAA,EACA,OAAO,OAAO,QAAQ,KAAKA,WAAU,CAAC,GAAG;AACvC,UAAM,UAAU,OAAO,MAAM,KAAK,eAAe;AACjD,QAAI,QAAQ,SAAS,KAAK,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI,KAAK,QAAQ,CAAC,EAAE,QAAQ,GAAG,IAAI,KAAK,QAAQ,KAAK,OAAK,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG;AAC9H,YAAM,YAAY,QAAQ,UAAU,OAAK,EAAE,QAAQ,GAAG,IAAI,EAAE;AAC5D,cAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,QAAQ,OAAO,GAAG,SAAS,CAAC,EAAE,KAAK,KAAK,eAAe;AAAA,IACtF;AACA,UAAM,SAAS,QAAQ,OAAO,CAAC,KAAK,MAAM;AAr0C9C;AAs0CM,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,MACF,IAAI,eAAe,CAAC;AACpB,UAAI,KAAK,QAAQ,UAAU,GAAG;AAC5B,YAAI,YAAY;AAChB,YAAI;AACF,gBAAM,eAAa,KAAAA,YAAA,gBAAAA,SAAS,iBAAT,mBAAwBA,SAAQ,sBAAqB,CAAC;AACzE,gBAAM,IAAI,WAAW,UAAU,WAAW,OAAOA,SAAQ,UAAUA,SAAQ,OAAO;AAClF,sBAAY,KAAK,QAAQ,UAAU,EAAE,KAAK,GAAG;AAAA,YAC3C,GAAG;AAAA,YACH,GAAGA;AAAA,YACH,GAAG;AAAA,UACL,CAAC;AAAA,QACH,SAAS,OAAO;AACd,eAAK,OAAO,KAAK,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,MACT,OAAO;AACL,aAAK,OAAO,KAAK,oCAAoC,UAAU,EAAE;AAAA,MACnE;AACA,aAAO;AAAA,IACT,GAAG,KAAK;AACR,WAAO;AAAA,EACT;AACF;AAEA,IAAM,gBAAgB,CAAC,GAAG,SAAS;AACjC,MAAI,EAAE,QAAQ,IAAI,MAAM,QAAW;AACjC,WAAO,EAAE,QAAQ,IAAI;AACrB,MAAE;AAAA,EACJ;AACF;AACA,IAAM,YAAN,cAAwB,aAAa;AAAA,EACnC,YAAY,SAAS,OAAO,UAAUA,WAAU,CAAC,GAAG;AAx2CtD;AAy2CI,UAAM;AACN,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,gBAAgB,SAAS;AAC9B,SAAK,UAAUA;AACf,SAAK,SAAS,WAAW,OAAO,kBAAkB;AAClD,SAAK,eAAe,CAAC;AACrB,SAAK,mBAAmBA,SAAQ,oBAAoB;AACpD,SAAK,eAAe;AACpB,SAAK,aAAaA,SAAQ,cAAc,IAAIA,SAAQ,aAAa;AACjE,SAAK,eAAeA,SAAQ,gBAAgB,IAAIA,SAAQ,eAAe;AACvE,SAAK,QAAQ,CAAC;AACd,SAAK,QAAQ,CAAC;AACd,qBAAK,YAAL,mBAAc,SAAd,4BAAqB,UAAUA,SAAQ,SAASA;AAAA,EAClD;AAAA,EACA,UAAU,WAAW,YAAYA,UAAS,UAAU;AAClD,UAAM,SAAS,CAAC;AAChB,UAAM,UAAU,CAAC;AACjB,UAAM,kBAAkB,CAAC;AACzB,UAAM,mBAAmB,CAAC;AAC1B,cAAU,QAAQ,SAAO;AACvB,UAAI,mBAAmB;AACvB,iBAAW,QAAQ,QAAM;AACvB,cAAM,OAAO,GAAG,GAAG,IAAI,EAAE;AACzB,YAAI,CAACA,SAAQ,UAAU,KAAK,MAAM,kBAAkB,KAAK,EAAE,GAAG;AAC5D,eAAK,MAAM,IAAI,IAAI;AAAA,QACrB,WAAW,KAAK,MAAM,IAAI,IAAI,EAAG;AAAA,iBAAW,KAAK,MAAM,IAAI,MAAM,GAAG;AAClE,cAAI,QAAQ,IAAI,MAAM,OAAW,SAAQ,IAAI,IAAI;AAAA,QACnD,OAAO;AACL,eAAK,MAAM,IAAI,IAAI;AACnB,6BAAmB;AACnB,cAAI,QAAQ,IAAI,MAAM,OAAW,SAAQ,IAAI,IAAI;AACjD,cAAI,OAAO,IAAI,MAAM,OAAW,QAAO,IAAI,IAAI;AAC/C,cAAI,iBAAiB,EAAE,MAAM,OAAW,kBAAiB,EAAE,IAAI;AAAA,QACjE;AAAA,MACF,CAAC;AACD,UAAI,CAAC,iBAAkB,iBAAgB,GAAG,IAAI;AAAA,IAChD,CAAC;AACD,QAAI,OAAO,KAAK,MAAM,EAAE,UAAU,OAAO,KAAK,OAAO,EAAE,QAAQ;AAC7D,WAAK,MAAM,KAAK;AAAA,QACd;AAAA,QACA,cAAc,OAAO,KAAK,OAAO,EAAE;AAAA,QACnC,QAAQ,CAAC;AAAA,QACT,QAAQ,CAAC;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AACA,WAAO;AAAA,MACL,QAAQ,OAAO,KAAK,MAAM;AAAA,MAC1B,SAAS,OAAO,KAAK,OAAO;AAAA,MAC5B,iBAAiB,OAAO,KAAK,eAAe;AAAA,MAC5C,kBAAkB,OAAO,KAAK,gBAAgB;AAAA,IAChD;AAAA,EACF;AAAA,EACA,OAAO,MAAM,KAAK,MAAM;AACtB,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,UAAM,MAAM,EAAE,CAAC;AACf,UAAM,KAAK,EAAE,CAAC;AACd,QAAI,IAAK,MAAK,KAAK,iBAAiB,KAAK,IAAI,GAAG;AAChD,QAAI,CAAC,OAAO,MAAM;AAChB,WAAK,MAAM,kBAAkB,KAAK,IAAI,MAAM,QAAW,QAAW;AAAA,QAChE,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,SAAK,MAAM,IAAI,IAAI,MAAM,KAAK;AAC9B,QAAI,OAAO,KAAM,MAAK,MAAM,IAAI,IAAI;AACpC,UAAM,SAAS,CAAC;AAChB,SAAK,MAAM,QAAQ,OAAK;AACtB,eAAS,EAAE,QAAQ,CAAC,GAAG,GAAG,EAAE;AAC5B,oBAAc,GAAG,IAAI;AACrB,UAAI,IAAK,GAAE,OAAO,KAAK,GAAG;AAC1B,UAAI,EAAE,iBAAiB,KAAK,CAAC,EAAE,MAAM;AACnC,eAAO,KAAK,EAAE,MAAM,EAAE,QAAQ,OAAK;AACjC,cAAI,CAAC,OAAO,CAAC,EAAG,QAAO,CAAC,IAAI,CAAC;AAC7B,gBAAM,aAAa,EAAE,OAAO,CAAC;AAC7B,cAAI,WAAW,QAAQ;AACrB,uBAAW,QAAQ,OAAK;AACtB,kBAAI,OAAO,CAAC,EAAE,CAAC,MAAM,OAAW,QAAO,CAAC,EAAE,CAAC,IAAI;AAAA,YACjD,CAAC;AAAA,UACH;AAAA,QACF,CAAC;AACD,UAAE,OAAO;AACT,YAAI,EAAE,OAAO,QAAQ;AACnB,YAAE,SAAS,EAAE,MAAM;AAAA,QACrB,OAAO;AACL,YAAE,SAAS;AAAA,QACb;AAAA,MACF;AAAA,IACF,CAAC;AACD,SAAK,KAAK,UAAU,MAAM;AAC1B,SAAK,QAAQ,KAAK,MAAM,OAAO,OAAK,CAAC,EAAE,IAAI;AAAA,EAC7C;AAAA,EACA,KAAK,KAAK,IAAI,QAAQ,QAAQ,GAAG,OAAO,KAAK,cAAc,UAAU;AACnE,QAAI,CAAC,IAAI,OAAQ,QAAO,SAAS,MAAM,CAAC,CAAC;AACzC,QAAI,KAAK,gBAAgB,KAAK,kBAAkB;AAC9C,WAAK,aAAa,KAAK;AAAA,QACrB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD;AAAA,IACF;AACA,SAAK;AACL,UAAM,WAAW,CAAC,KAAK,SAAS;AAC9B,WAAK;AACL,UAAI,KAAK,aAAa,SAAS,GAAG;AAChC,cAAM,OAAO,KAAK,aAAa,MAAM;AACrC,aAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,OAAO,KAAK,MAAM,KAAK,QAAQ;AAAA,MAChF;AACA,UAAI,OAAO,QAAQ,QAAQ,KAAK,YAAY;AAC1C,mBAAW,MAAM;AACf,eAAK,KAAK,KAAK,MAAM,KAAK,IAAI,QAAQ,QAAQ,GAAG,OAAO,GAAG,QAAQ;AAAA,QACrE,GAAG,IAAI;AACP;AAAA,MACF;AACA,eAAS,KAAK,IAAI;AAAA,IACpB;AACA,UAAM,KAAK,KAAK,QAAQ,MAAM,EAAE,KAAK,KAAK,OAAO;AACjD,QAAI,GAAG,WAAW,GAAG;AACnB,UAAI;AACF,cAAM,IAAI,GAAG,KAAK,EAAE;AACpB,YAAI,KAAK,OAAO,EAAE,SAAS,YAAY;AACrC,YAAE,KAAK,UAAQ,SAAS,MAAM,IAAI,CAAC,EAAE,MAAM,QAAQ;AAAA,QACrD,OAAO;AACL,mBAAS,MAAM,CAAC;AAAA,QAClB;AAAA,MACF,SAAS,KAAK;AACZ,iBAAS,GAAG;AAAA,MACd;AACA;AAAA,IACF;AACA,WAAO,GAAG,KAAK,IAAI,QAAQ;AAAA,EAC7B;AAAA,EACA,eAAe,WAAW,YAAYA,WAAU,CAAC,GAAG,UAAU;AAC5D,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,OAAO,KAAK,gEAAgE;AACjF,aAAO,YAAY,SAAS;AAAA,IAC9B;AACA,QAAI,SAAS,SAAS,EAAG,aAAY,KAAK,cAAc,mBAAmB,SAAS;AACpF,QAAI,SAAS,UAAU,EAAG,cAAa,CAAC,UAAU;AAClD,UAAM,SAAS,KAAK,UAAU,WAAW,YAAYA,UAAS,QAAQ;AACtE,QAAI,CAAC,OAAO,OAAO,QAAQ;AACzB,UAAI,CAAC,OAAO,QAAQ,OAAQ,UAAS;AACrC,aAAO;AAAA,IACT;AACA,WAAO,OAAO,QAAQ,UAAQ;AAC5B,WAAK,QAAQ,IAAI;AAAA,IACnB,CAAC;AAAA,EACH;AAAA,EACA,KAAK,WAAW,YAAY,UAAU;AACpC,SAAK,eAAe,WAAW,YAAY,CAAC,GAAG,QAAQ;AAAA,EACzD;AAAA,EACA,OAAO,WAAW,YAAY,UAAU;AACtC,SAAK,eAAe,WAAW,YAAY;AAAA,MACzC,QAAQ;AAAA,IACV,GAAG,QAAQ;AAAA,EACb;AAAA,EACA,QAAQ,MAAM,SAAS,IAAI;AACzB,UAAM,IAAI,KAAK,MAAM,GAAG;AACxB,UAAM,MAAM,EAAE,CAAC;AACf,UAAM,KAAK,EAAE,CAAC;AACd,SAAK,KAAK,KAAK,IAAI,QAAQ,QAAW,QAAW,CAAC,KAAK,SAAS;AAC9D,UAAI,IAAK,MAAK,OAAO,KAAK,GAAG,MAAM,qBAAqB,EAAE,iBAAiB,GAAG,WAAW,GAAG;AAC5F,UAAI,CAAC,OAAO,KAAM,MAAK,OAAO,IAAI,GAAG,MAAM,oBAAoB,EAAE,iBAAiB,GAAG,IAAI,IAAI;AAC7F,WAAK,OAAO,MAAM,KAAK,IAAI;AAAA,IAC7B,CAAC;AAAA,EACH;AAAA,EACA,YAAY,WAAW,WAAW,KAAK,eAAe,UAAUA,WAAU,CAAC,GAAG,MAAM,MAAM;AAAA,EAAC,GAAG;AAphDhG;AAqhDI,UAAI,gBAAK,aAAL,mBAAe,UAAf,mBAAsB,uBAAsB,GAAC,gBAAK,aAAL,mBAAe,UAAf,mBAAsB,mBAAmB,aAAY;AACpG,WAAK,OAAO,KAAK,qBAAqB,GAAG,uBAAuB,SAAS,wBAAwB,0NAA0N;AAC3T;AAAA,IACF;AACA,QAAI,QAAQ,UAAa,QAAQ,QAAQ,QAAQ,GAAI;AACrD,SAAI,UAAK,YAAL,mBAAc,QAAQ;AACxB,YAAM,OAAO;AAAA,QACX,GAAGA;AAAA,QACH;AAAA,MACF;AACA,YAAM,KAAK,KAAK,QAAQ,OAAO,KAAK,KAAK,OAAO;AAChD,UAAI,GAAG,SAAS,GAAG;AACjB,YAAI;AACF,cAAI;AACJ,cAAI,GAAG,WAAW,GAAG;AACnB,gBAAI,GAAG,WAAW,WAAW,KAAK,eAAe,IAAI;AAAA,UACvD,OAAO;AACL,gBAAI,GAAG,WAAW,WAAW,KAAK,aAAa;AAAA,UACjD;AACA,cAAI,KAAK,OAAO,EAAE,SAAS,YAAY;AACrC,cAAE,KAAK,UAAQ,IAAI,MAAM,IAAI,CAAC,EAAE,MAAM,GAAG;AAAA,UAC3C,OAAO;AACL,gBAAI,MAAM,CAAC;AAAA,UACb;AAAA,QACF,SAAS,KAAK;AACZ,cAAI,GAAG;AAAA,QACT;AAAA,MACF,OAAO;AACL,WAAG,WAAW,WAAW,KAAK,eAAe,KAAK,IAAI;AAAA,MACxD;AAAA,IACF;AACA,QAAI,CAAC,aAAa,CAAC,UAAU,CAAC,EAAG;AACjC,SAAK,MAAM,YAAY,UAAU,CAAC,GAAG,WAAW,KAAK,aAAa;AAAA,EACpE;AACF;AAEA,IAAM,MAAM,OAAO;AAAA,EACjB,OAAO;AAAA,EACP,WAAW;AAAA,EACX,IAAI,CAAC,aAAa;AAAA,EAClB,WAAW,CAAC,aAAa;AAAA,EACzB,aAAa,CAAC,KAAK;AAAA,EACnB,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,MAAM;AAAA,EACN,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,cAAc;AAAA,EACd,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,aAAa;AAAA,EACb,eAAe;AAAA,EACf,eAAe;AAAA,EACf,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,6BAA6B;AAAA,EAC7B,aAAa;AAAA,EACb,yBAAyB;AAAA,EACzB,YAAY;AAAA,EACZ,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,uBAAuB;AAAA,EACvB,wBAAwB;AAAA,EACxB,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,kCAAkC,UAAQ;AACxC,QAAI,MAAM,CAAC;AACX,QAAI,OAAO,KAAK,CAAC,MAAM,SAAU,OAAM,KAAK,CAAC;AAC7C,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,KAAI,eAAe,KAAK,CAAC;AAChD,QAAI,SAAS,KAAK,CAAC,CAAC,EAAG,KAAI,eAAe,KAAK,CAAC;AAChD,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY,OAAO,KAAK,CAAC,MAAM,UAAU;AAC9D,YAAMA,WAAU,KAAK,CAAC,KAAK,KAAK,CAAC;AACjC,aAAO,KAAKA,QAAO,EAAE,QAAQ,SAAO;AAClC,YAAI,GAAG,IAAIA,SAAQ,GAAG;AAAA,MACxB,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AAAA,IACb,aAAa;AAAA,IACb,QAAQ,WAAS;AAAA,IACjB,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,eAAe;AAAA,IACf,yBAAyB;AAAA,IACzB,aAAa;AAAA,IACb,iBAAiB;AAAA,EACnB;AAAA,EACA,qBAAqB;AACvB;AACA,IAAM,mBAAmB,CAAAA,aAAW;AAtnDpC;AAunDE,MAAI,SAASA,SAAQ,EAAE,EAAG,CAAAA,SAAQ,KAAK,CAACA,SAAQ,EAAE;AAClD,MAAI,SAASA,SAAQ,WAAW,EAAG,CAAAA,SAAQ,cAAc,CAACA,SAAQ,WAAW;AAC7E,MAAI,SAASA,SAAQ,UAAU,EAAG,CAAAA,SAAQ,aAAa,CAACA,SAAQ,UAAU;AAC1E,QAAI,WAAAA,SAAQ,kBAAR,mBAAuB,YAAvB,4BAAiC,aAAY,GAAG;AAClD,IAAAA,SAAQ,gBAAgBA,SAAQ,cAAc,OAAO,CAAC,QAAQ,CAAC;AAAA,EACjE;AACA,MAAI,OAAOA,SAAQ,kBAAkB,UAAW,CAAAA,SAAQ,YAAYA,SAAQ;AAC5E,SAAOA;AACT;AAEA,IAAM,OAAO,MAAM;AAAC;AACpB,IAAM,sBAAsB,UAAQ;AAClC,QAAM,OAAO,OAAO,oBAAoB,OAAO,eAAe,IAAI,CAAC;AACnE,OAAK,QAAQ,SAAO;AAClB,QAAI,OAAO,KAAK,GAAG,MAAM,YAAY;AACnC,WAAK,GAAG,IAAI,KAAK,GAAG,EAAE,KAAK,IAAI;AAAA,IACjC;AAAA,EACF,CAAC;AACH;AACA,IAAM,OAAN,MAAM,cAAa,aAAa;AAAA,EAC9B,YAAYA,WAAU,CAAC,GAAG,UAAU;AAClC,UAAM;AACN,SAAK,UAAU,iBAAiBA,QAAO;AACvC,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,MACb,UAAU,CAAC;AAAA,IACb;AACA,wBAAoB,IAAI;AACxB,QAAI,YAAY,CAAC,KAAK,iBAAiB,CAACA,SAAQ,SAAS;AACvD,UAAI,CAAC,KAAK,QAAQ,WAAW;AAC3B,aAAK,KAAKA,UAAS,QAAQ;AAC3B,eAAO;AAAA,MACT;AACA,iBAAW,MAAM;AACf,aAAK,KAAKA,UAAS,QAAQ;AAAA,MAC7B,GAAG,CAAC;AAAA,IACN;AAAA,EACF;AAAA,EACA,KAAKA,WAAU,CAAC,GAAG,UAAU;AAC3B,SAAK,iBAAiB;AACtB,QAAI,OAAOA,aAAY,YAAY;AACjC,iBAAWA;AACX,MAAAA,WAAU,CAAC;AAAA,IACb;AACA,QAAIA,SAAQ,aAAa,QAAQA,SAAQ,IAAI;AAC3C,UAAI,SAASA,SAAQ,EAAE,GAAG;AACxB,QAAAA,SAAQ,YAAYA,SAAQ;AAAA,MAC9B,WAAWA,SAAQ,GAAG,QAAQ,aAAa,IAAI,GAAG;AAChD,QAAAA,SAAQ,YAAYA,SAAQ,GAAG,CAAC;AAAA,MAClC;AAAA,IACF;AACA,UAAM,UAAU,IAAI;AACpB,SAAK,UAAU;AAAA,MACb,GAAG;AAAA,MACH,GAAG,KAAK;AAAA,MACR,GAAG,iBAAiBA,QAAO;AAAA,IAC7B;AACA,SAAK,QAAQ,gBAAgB;AAAA,MAC3B,GAAG,QAAQ;AAAA,MACX,GAAG,KAAK,QAAQ;AAAA,IAClB;AACA,QAAIA,SAAQ,iBAAiB,QAAW;AACtC,WAAK,QAAQ,0BAA0BA,SAAQ;AAAA,IACjD;AACA,QAAIA,SAAQ,gBAAgB,QAAW;AACrC,WAAK,QAAQ,yBAAyBA,SAAQ;AAAA,IAChD;AACA,UAAM,sBAAsB,mBAAiB;AAC3C,UAAI,CAAC,cAAe,QAAO;AAC3B,UAAI,OAAO,kBAAkB,WAAY,QAAO,IAAI,cAAc;AAClE,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,QAAQ,SAAS;AACzB,UAAI,KAAK,QAAQ,QAAQ;AACvB,mBAAW,KAAK,oBAAoB,KAAK,QAAQ,MAAM,GAAG,KAAK,OAAO;AAAA,MACxE,OAAO;AACL,mBAAW,KAAK,MAAM,KAAK,OAAO;AAAA,MACpC;AACA,UAAI;AACJ,UAAI,KAAK,QAAQ,WAAW;AAC1B,oBAAY,KAAK,QAAQ;AAAA,MAC3B,OAAO;AACL,oBAAY;AAAA,MACd;AACA,YAAM,KAAK,IAAI,aAAa,KAAK,OAAO;AACxC,WAAK,QAAQ,IAAI,cAAc,KAAK,QAAQ,WAAW,KAAK,OAAO;AACnE,YAAM,IAAI,KAAK;AACf,QAAE,SAAS;AACX,QAAE,gBAAgB,KAAK;AACvB,QAAE,gBAAgB;AAClB,QAAE,iBAAiB,IAAI,eAAe,IAAI;AAAA,QACxC,SAAS,KAAK,QAAQ;AAAA,QACtB,sBAAsB,KAAK,QAAQ;AAAA,MACrC,CAAC;AACD,UAAI,cAAc,CAAC,KAAK,QAAQ,cAAc,UAAU,KAAK,QAAQ,cAAc,WAAW,QAAQ,cAAc,SAAS;AAC3H,UAAE,YAAY,oBAAoB,SAAS;AAC3C,UAAE,UAAU,KAAK,GAAG,KAAK,OAAO;AAChC,aAAK,QAAQ,cAAc,SAAS,EAAE,UAAU,OAAO,KAAK,EAAE,SAAS;AAAA,MACzE;AACA,QAAE,eAAe,IAAI,aAAa,KAAK,OAAO;AAC9C,QAAE,QAAQ;AAAA,QACR,oBAAoB,KAAK,mBAAmB,KAAK,IAAI;AAAA,MACvD;AACA,QAAE,mBAAmB,IAAI,UAAU,oBAAoB,KAAK,QAAQ,OAAO,GAAG,EAAE,eAAe,GAAG,KAAK,OAAO;AAC9G,QAAE,iBAAiB,GAAG,KAAK,CAAC,UAAU,SAAS;AAC7C,aAAK,KAAK,OAAO,GAAG,IAAI;AAAA,MAC1B,CAAC;AACD,UAAI,KAAK,QAAQ,kBAAkB;AACjC,UAAE,mBAAmB,oBAAoB,KAAK,QAAQ,gBAAgB;AACtE,YAAI,EAAE,iBAAiB,KAAM,GAAE,iBAAiB,KAAK,GAAG,KAAK,QAAQ,WAAW,KAAK,OAAO;AAAA,MAC9F;AACA,UAAI,KAAK,QAAQ,YAAY;AAC3B,UAAE,aAAa,oBAAoB,KAAK,QAAQ,UAAU;AAC1D,YAAI,EAAE,WAAW,KAAM,GAAE,WAAW,KAAK,IAAI;AAAA,MAC/C;AACA,WAAK,aAAa,IAAI,WAAW,KAAK,UAAU,KAAK,OAAO;AAC5D,WAAK,WAAW,GAAG,KAAK,CAAC,UAAU,SAAS;AAC1C,aAAK,KAAK,OAAO,GAAG,IAAI;AAAA,MAC1B,CAAC;AACD,WAAK,QAAQ,SAAS,QAAQ,OAAK;AACjC,YAAI,EAAE,KAAM,GAAE,KAAK,IAAI;AAAA,MACzB,CAAC;AAAA,IACH;AACA,SAAK,SAAS,KAAK,QAAQ,cAAc;AACzC,QAAI,CAAC,SAAU,YAAW;AAC1B,QAAI,KAAK,QAAQ,eAAe,CAAC,KAAK,SAAS,oBAAoB,CAAC,KAAK,QAAQ,KAAK;AACpF,YAAM,QAAQ,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW;AACnF,UAAI,MAAM,SAAS,KAAK,MAAM,CAAC,MAAM,MAAO,MAAK,QAAQ,MAAM,MAAM,CAAC;AAAA,IACxE;AACA,QAAI,CAAC,KAAK,SAAS,oBAAoB,CAAC,KAAK,QAAQ,KAAK;AACxD,WAAK,OAAO,KAAK,yDAAyD;AAAA,IAC5E;AACA,UAAM,WAAW,CAAC,eAAe,qBAAqB,qBAAqB,mBAAmB;AAC9F,aAAS,QAAQ,YAAU;AACzB,WAAK,MAAM,IAAI,IAAI,SAAS,KAAK,MAAM,MAAM,EAAE,GAAG,IAAI;AAAA,IACxD,CAAC;AACD,UAAM,kBAAkB,CAAC,eAAe,gBAAgB,qBAAqB,sBAAsB;AACnG,oBAAgB,QAAQ,YAAU;AAChC,WAAK,MAAM,IAAI,IAAI,SAAS;AAC1B,aAAK,MAAM,MAAM,EAAE,GAAG,IAAI;AAC1B,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AACD,UAAM,WAAW,MAAM;AACvB,UAAM,OAAO,MAAM;AACjB,YAAM,SAAS,CAAC,KAAKD,OAAM;AACzB,aAAK,iBAAiB;AACtB,YAAI,KAAK,iBAAiB,CAAC,KAAK,qBAAsB,MAAK,OAAO,KAAK,uEAAuE;AAC9I,aAAK,gBAAgB;AACrB,YAAI,CAAC,KAAK,QAAQ,QAAS,MAAK,OAAO,IAAI,eAAe,KAAK,OAAO;AACtE,aAAK,KAAK,eAAe,KAAK,OAAO;AACrC,iBAAS,QAAQA,EAAC;AAClB,iBAAS,KAAKA,EAAC;AAAA,MACjB;AACA,UAAI,KAAK,aAAa,CAAC,KAAK,cAAe,QAAO,OAAO,MAAM,KAAK,EAAE,KAAK,IAAI,CAAC;AAChF,WAAK,eAAe,KAAK,QAAQ,KAAK,MAAM;AAAA,IAC9C;AACA,QAAI,KAAK,QAAQ,aAAa,CAAC,KAAK,QAAQ,WAAW;AACrD,WAAK;AAAA,IACP,OAAO;AACL,iBAAW,MAAM,CAAC;AAAA,IACpB;AACA,WAAO;AAAA,EACT;AAAA,EACA,cAAc,UAAU,WAAW,MAAM;AA5xD3C;AA6xDI,QAAI,eAAe;AACnB,UAAM,UAAU,SAAS,QAAQ,IAAI,WAAW,KAAK;AACrD,QAAI,OAAO,aAAa,WAAY,gBAAe;AACnD,QAAI,CAAC,KAAK,QAAQ,aAAa,KAAK,QAAQ,yBAAyB;AACnE,WAAI,mCAAS,mBAAkB,aAAa,CAAC,KAAK,QAAQ,WAAW,KAAK,QAAQ,QAAQ,WAAW,GAAI,QAAO,aAAa;AAC7H,YAAM,SAAS,CAAC;AAChB,YAAM,SAAS,SAAO;AACpB,YAAI,CAAC,IAAK;AACV,YAAI,QAAQ,SAAU;AACtB,cAAM,OAAO,KAAK,SAAS,cAAc,mBAAmB,GAAG;AAC/D,aAAK,QAAQ,OAAK;AAChB,cAAI,MAAM,SAAU;AACpB,cAAI,OAAO,QAAQ,CAAC,IAAI,EAAG,QAAO,KAAK,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AACA,UAAI,CAAC,SAAS;AACZ,cAAM,YAAY,KAAK,SAAS,cAAc,iBAAiB,KAAK,QAAQ,WAAW;AACvF,kBAAU,QAAQ,OAAK,OAAO,CAAC,CAAC;AAAA,MAClC,OAAO;AACL,eAAO,OAAO;AAAA,MAChB;AACA,uBAAK,QAAQ,YAAb,mBAAsB,YAAtB,4BAAgC,OAAK,OAAO,CAAC;AAC7C,WAAK,SAAS,iBAAiB,KAAK,QAAQ,KAAK,QAAQ,IAAI,OAAK;AAChE,YAAI,CAAC,KAAK,CAAC,KAAK,oBAAoB,KAAK,SAAU,MAAK,oBAAoB,KAAK,QAAQ;AACzF,qBAAa,CAAC;AAAA,MAChB,CAAC;AAAA,IACH,OAAO;AACL,mBAAa,IAAI;AAAA,IACnB;AAAA,EACF;AAAA,EACA,gBAAgB,MAAM,IAAI,UAAU;AAClC,UAAM,WAAW,MAAM;AACvB,QAAI,OAAO,SAAS,YAAY;AAC9B,iBAAW;AACX,aAAO;AAAA,IACT;AACA,QAAI,OAAO,OAAO,YAAY;AAC5B,iBAAW;AACX,WAAK;AAAA,IACP;AACA,QAAI,CAAC,KAAM,QAAO,KAAK;AACvB,QAAI,CAAC,GAAI,MAAK,KAAK,QAAQ;AAC3B,QAAI,CAAC,SAAU,YAAW;AAC1B,SAAK,SAAS,iBAAiB,OAAO,MAAM,IAAI,SAAO;AACrD,eAAS,QAAQ;AACjB,eAAS,GAAG;AAAA,IACd,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,QAAQ;AACV,QAAI,CAAC,OAAQ,OAAM,IAAI,MAAM,+FAA+F;AAC5H,QAAI,CAAC,OAAO,KAAM,OAAM,IAAI,MAAM,0FAA0F;AAC5H,QAAI,OAAO,SAAS,WAAW;AAC7B,WAAK,QAAQ,UAAU;AAAA,IACzB;AACA,QAAI,OAAO,SAAS,YAAY,OAAO,OAAO,OAAO,QAAQ,OAAO,OAAO;AACzE,WAAK,QAAQ,SAAS;AAAA,IACxB;AACA,QAAI,OAAO,SAAS,oBAAoB;AACtC,WAAK,QAAQ,mBAAmB;AAAA,IAClC;AACA,QAAI,OAAO,SAAS,cAAc;AAChC,WAAK,QAAQ,aAAa;AAAA,IAC5B;AACA,QAAI,OAAO,SAAS,iBAAiB;AACnC,oBAAc,iBAAiB,MAAM;AAAA,IACvC;AACA,QAAI,OAAO,SAAS,aAAa;AAC/B,WAAK,QAAQ,YAAY;AAAA,IAC3B;AACA,QAAI,OAAO,SAAS,YAAY;AAC9B,WAAK,QAAQ,SAAS,KAAK,MAAM;AAAA,IACnC;AACA,WAAO;AAAA,EACT;AAAA,EACA,oBAAoB,GAAG;AACrB,QAAI,CAAC,KAAK,CAAC,KAAK,UAAW;AAC3B,QAAI,CAAC,UAAU,KAAK,EAAE,QAAQ,CAAC,IAAI,GAAI;AACvC,aAAS,KAAK,GAAG,KAAK,KAAK,UAAU,QAAQ,MAAM;AACjD,YAAM,YAAY,KAAK,UAAU,EAAE;AACnC,UAAI,CAAC,UAAU,KAAK,EAAE,QAAQ,SAAS,IAAI,GAAI;AAC/C,UAAI,KAAK,MAAM,4BAA4B,SAAS,GAAG;AACrD,aAAK,mBAAmB;AACxB;AAAA,MACF;AAAA,IACF;AACA,QAAI,CAAC,KAAK,oBAAoB,KAAK,UAAU,QAAQ,CAAC,IAAI,KAAK,KAAK,MAAM,4BAA4B,CAAC,GAAG;AACxG,WAAK,mBAAmB;AACxB,WAAK,UAAU,QAAQ,CAAC;AAAA,IAC1B;AAAA,EACF;AAAA,EACA,eAAe,KAAK,UAAU;AAC5B,SAAK,uBAAuB;AAC5B,UAAM,WAAW,MAAM;AACvB,SAAK,KAAK,oBAAoB,GAAG;AACjC,UAAM,cAAc,OAAK;AACvB,WAAK,WAAW;AAChB,WAAK,YAAY,KAAK,SAAS,cAAc,mBAAmB,CAAC;AACjE,WAAK,mBAAmB;AACxB,WAAK,oBAAoB,CAAC;AAAA,IAC5B;AACA,UAAM,OAAO,CAAC,KAAK,MAAM;AACvB,UAAI,GAAG;AACL,YAAI,KAAK,yBAAyB,KAAK;AACrC,sBAAY,CAAC;AACb,eAAK,WAAW,eAAe,CAAC;AAChC,eAAK,uBAAuB;AAC5B,eAAK,KAAK,mBAAmB,CAAC;AAC9B,eAAK,OAAO,IAAI,mBAAmB,CAAC;AAAA,QACtC;AAAA,MACF,OAAO;AACL,aAAK,uBAAuB;AAAA,MAC9B;AACA,eAAS,QAAQ,IAAI,SAAS,KAAK,EAAE,GAAG,IAAI,CAAC;AAC7C,UAAI,SAAU,UAAS,KAAK,IAAI,SAAS,KAAK,EAAE,GAAG,IAAI,CAAC;AAAA,IAC1D;AACA,UAAM,SAAS,UAAQ;AAj5D3B;AAk5DM,UAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,SAAS,iBAAkB,QAAO,CAAC;AAC7D,YAAM,KAAK,SAAS,IAAI,IAAI,OAAO,QAAQ,KAAK,CAAC;AACjD,YAAM,IAAI,KAAK,MAAM,4BAA4B,EAAE,IAAI,KAAK,KAAK,SAAS,cAAc,sBAAsB,SAAS,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI;AAC5I,UAAI,GAAG;AACL,YAAI,CAAC,KAAK,UAAU;AAClB,sBAAY,CAAC;AAAA,QACf;AACA,YAAI,CAAC,KAAK,WAAW,SAAU,MAAK,WAAW,eAAe,CAAC;AAC/D,yBAAK,SAAS,qBAAd,mBAAgC,sBAAhC,4BAAoD;AAAA,MACtD;AACA,WAAK,cAAc,GAAG,SAAO;AAC3B,aAAK,KAAK,CAAC;AAAA,MACb,CAAC;AAAA,IACH;AACA,QAAI,CAAC,OAAO,KAAK,SAAS,oBAAoB,CAAC,KAAK,SAAS,iBAAiB,OAAO;AACnF,aAAO,KAAK,SAAS,iBAAiB,OAAO,CAAC;AAAA,IAChD,WAAW,CAAC,OAAO,KAAK,SAAS,oBAAoB,KAAK,SAAS,iBAAiB,OAAO;AACzF,UAAI,KAAK,SAAS,iBAAiB,OAAO,WAAW,GAAG;AACtD,aAAK,SAAS,iBAAiB,OAAO,EAAE,KAAK,MAAM;AAAA,MACrD,OAAO;AACL,aAAK,SAAS,iBAAiB,OAAO,MAAM;AAAA,MAC9C;AAAA,IACF,OAAO;AACL,aAAO,GAAG;AAAA,IACZ;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,KAAK,IAAI,WAAW;AAC5B,UAAM,SAAS,CAAC,KAAK,SAAS,SAAS;AACrC,UAAI;AACJ,UAAI,OAAO,SAAS,UAAU;AAC5B,YAAI,KAAK,QAAQ,iCAAiC,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,MAC5E,OAAO;AACL,YAAI;AAAA,UACF,GAAG;AAAA,QACL;AAAA,MACF;AACA,QAAE,MAAM,EAAE,OAAO,OAAO;AACxB,QAAE,OAAO,EAAE,QAAQ,OAAO;AAC1B,QAAE,KAAK,EAAE,MAAM,OAAO;AACtB,UAAI,EAAE,cAAc,GAAI,GAAE,YAAY,EAAE,aAAa,aAAa,OAAO;AACzE,YAAM,eAAe,KAAK,QAAQ,gBAAgB;AAClD,UAAI;AACJ,UAAI,EAAE,aAAa,MAAM,QAAQ,GAAG,GAAG;AACrC,oBAAY,IAAI,IAAI,OAAK,GAAG,EAAE,SAAS,GAAG,YAAY,GAAG,CAAC,EAAE;AAAA,MAC9D,OAAO;AACL,oBAAY,EAAE,YAAY,GAAG,EAAE,SAAS,GAAG,YAAY,GAAG,GAAG,KAAK;AAAA,MACpE;AACA,aAAO,KAAK,EAAE,WAAW,CAAC;AAAA,IAC5B;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO,MAAM;AAAA,IACf,OAAO;AACL,aAAO,OAAO;AAAA,IAChB;AACA,WAAO,KAAK;AACZ,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAAA,EACA,KAAK,MAAM;AA78Db;AA88DI,YAAO,UAAK,eAAL,mBAAiB,UAAU,GAAG;AAAA,EACvC;AAAA,EACA,UAAU,MAAM;AAh9DlB;AAi9DI,YAAO,UAAK,eAAL,mBAAiB,OAAO,GAAG;AAAA,EACpC;AAAA,EACA,oBAAoB,IAAI;AACtB,SAAK,QAAQ,YAAY;AAAA,EAC3B;AAAA,EACA,mBAAmB,IAAIC,WAAU,CAAC,GAAG;AACnC,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,OAAO,KAAK,mDAAmD,KAAK,SAAS;AAClF,aAAO;AAAA,IACT;AACA,QAAI,CAAC,KAAK,aAAa,CAAC,KAAK,UAAU,QAAQ;AAC7C,WAAK,OAAO,KAAK,8DAA8D,KAAK,SAAS;AAC7F,aAAO;AAAA,IACT;AACA,UAAM,MAAMA,SAAQ,OAAO,KAAK,oBAAoB,KAAK,UAAU,CAAC;AACpE,UAAM,cAAc,KAAK,UAAU,KAAK,QAAQ,cAAc;AAC9D,UAAM,UAAU,KAAK,UAAU,KAAK,UAAU,SAAS,CAAC;AACxD,QAAI,IAAI,YAAY,MAAM,SAAU,QAAO;AAC3C,UAAM,iBAAiB,CAAC,GAAG,MAAM;AAC/B,YAAM,YAAY,KAAK,SAAS,iBAAiB,MAAM,GAAG,CAAC,IAAI,CAAC,EAAE;AAClE,aAAO,cAAc,MAAM,cAAc,KAAK,cAAc;AAAA,IAC9D;AACA,QAAIA,SAAQ,UAAU;AACpB,YAAM,YAAYA,SAAQ,SAAS,MAAM,cAAc;AACvD,UAAI,cAAc,OAAW,QAAO;AAAA,IACtC;AACA,QAAI,KAAK,kBAAkB,KAAK,EAAE,EAAG,QAAO;AAC5C,QAAI,CAAC,KAAK,SAAS,iBAAiB,WAAW,KAAK,QAAQ,aAAa,CAAC,KAAK,QAAQ,wBAAyB,QAAO;AACvH,QAAI,eAAe,KAAK,EAAE,MAAM,CAAC,eAAe,eAAe,SAAS,EAAE,GAAI,QAAO;AACrF,WAAO;AAAA,EACT;AAAA,EACA,eAAe,IAAI,UAAU;AAC3B,UAAM,WAAW,MAAM;AACvB,QAAI,CAAC,KAAK,QAAQ,IAAI;AACpB,UAAI,SAAU,UAAS;AACvB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,QAAI,SAAS,EAAE,EAAG,MAAK,CAAC,EAAE;AAC1B,OAAG,QAAQ,OAAK;AACd,UAAI,KAAK,QAAQ,GAAG,QAAQ,CAAC,IAAI,EAAG,MAAK,QAAQ,GAAG,KAAK,CAAC;AAAA,IAC5D,CAAC;AACD,SAAK,cAAc,SAAO;AACxB,eAAS,QAAQ;AACjB,UAAI,SAAU,UAAS,GAAG;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,cAAc,MAAM,UAAU;AAC5B,UAAM,WAAW,MAAM;AACvB,QAAI,SAAS,IAAI,EAAG,QAAO,CAAC,IAAI;AAChC,UAAM,YAAY,KAAK,QAAQ,WAAW,CAAC;AAC3C,UAAM,UAAU,KAAK,OAAO,SAAO,UAAU,QAAQ,GAAG,IAAI,KAAK,KAAK,SAAS,cAAc,gBAAgB,GAAG,CAAC;AACjH,QAAI,CAAC,QAAQ,QAAQ;AACnB,UAAI,SAAU,UAAS;AACvB,aAAO,QAAQ,QAAQ;AAAA,IACzB;AACA,SAAK,QAAQ,UAAU,UAAU,OAAO,OAAO;AAC/C,SAAK,cAAc,SAAO;AACxB,eAAS,QAAQ;AACjB,UAAI,SAAU,UAAS,GAAG;AAAA,IAC5B,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,IAAI,KAAK;AAhhEX;AAihEI,QAAI,CAAC,IAAK,OAAM,KAAK,uBAAqB,UAAK,cAAL,mBAAgB,UAAS,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK;AAChG,QAAI,CAAC,IAAK,QAAO;AACjB,UAAM,UAAU,CAAC,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,MAAM,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AACvb,UAAM,kBAAgB,UAAK,aAAL,mBAAe,kBAAiB,IAAI,aAAa,IAAI,CAAC;AAC5E,WAAO,QAAQ,QAAQ,cAAc,wBAAwB,GAAG,CAAC,IAAI,MAAM,IAAI,YAAY,EAAE,QAAQ,OAAO,IAAI,IAAI,QAAQ;AAAA,EAC9H;AAAA,EACA,OAAO,eAAeA,WAAU,CAAC,GAAG,UAAU;AAC5C,WAAO,IAAI,MAAKA,UAAS,QAAQ;AAAA,EACnC;AAAA,EACA,cAAcA,WAAU,CAAC,GAAG,WAAW,MAAM;AAC3C,UAAM,oBAAoBA,SAAQ;AAClC,QAAI,kBAAmB,QAAOA,SAAQ;AACtC,UAAM,gBAAgB;AAAA,MACpB,GAAG,KAAK;AAAA,MACR,GAAGA;AAAA,MACH,GAAG;AAAA,QACD,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,MAAK,aAAa;AACpC,QAAIA,SAAQ,UAAU,UAAaA,SAAQ,WAAW,QAAW;AAC/D,YAAM,SAAS,MAAM,OAAO,MAAMA,QAAO;AAAA,IAC3C;AACA,UAAM,gBAAgB,CAAC,SAAS,YAAY,UAAU;AACtD,kBAAc,QAAQ,OAAK;AACzB,YAAM,CAAC,IAAI,KAAK,CAAC;AAAA,IACnB,CAAC;AACD,UAAM,WAAW;AAAA,MACf,GAAG,KAAK;AAAA,IACV;AACA,UAAM,SAAS,QAAQ;AAAA,MACrB,oBAAoB,MAAM,mBAAmB,KAAK,KAAK;AAAA,IACzD;AACA,QAAI,mBAAmB;AACrB,YAAM,aAAa,OAAO,KAAK,KAAK,MAAM,IAAI,EAAE,OAAO,CAAC,MAAM,MAAM;AAClE,aAAK,CAAC,IAAI;AAAA,UACR,GAAG,KAAK,MAAM,KAAK,CAAC;AAAA,QACtB;AACA,aAAK,CAAC,IAAI,OAAO,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,MAAM;AAChD,cAAI,CAAC,IAAI;AAAA,YACP,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,UACd;AACA,iBAAO;AAAA,QACT,GAAG,KAAK,CAAC,CAAC;AACV,eAAO;AAAA,MACT,GAAG,CAAC,CAAC;AACL,YAAM,QAAQ,IAAI,cAAc,YAAY,aAAa;AACzD,YAAM,SAAS,gBAAgB,MAAM;AAAA,IACvC;AACA,UAAM,aAAa,IAAI,WAAW,MAAM,UAAU,aAAa;AAC/D,UAAM,WAAW,GAAG,KAAK,CAAC,UAAU,SAAS;AAC3C,YAAM,KAAK,OAAO,GAAG,IAAI;AAAA,IAC3B,CAAC;AACD,UAAM,KAAK,eAAe,QAAQ;AAClC,UAAM,WAAW,UAAU;AAC3B,UAAM,WAAW,iBAAiB,SAAS,QAAQ;AAAA,MACjD,oBAAoB,MAAM,mBAAmB,KAAK,KAAK;AAAA,IACzD;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,WAAO;AAAA,MACL,SAAS,KAAK;AAAA,MACd,OAAO,KAAK;AAAA,MACZ,UAAU,KAAK;AAAA,MACf,WAAW,KAAK;AAAA,MAChB,kBAAkB,KAAK;AAAA,IACzB;AAAA,EACF;AACF;AACA,IAAM,WAAW,KAAK,eAAe;AACrC,SAAS,iBAAiB,KAAK;AAE/B,IAAM,iBAAiB,SAAS;AAChC,IAAM,MAAM,SAAS;AACrB,IAAM,OAAO,SAAS;AACtB,IAAM,gBAAgB,SAAS;AAC/B,IAAM,kBAAkB,SAAS;AACjC,IAAM,MAAM,SAAS;AACrB,IAAM,iBAAiB,SAAS;AAChC,IAAM,YAAY,SAAS;AAC3B,IAAM,IAAI,SAAS;AACnB,IAAM,SAAS,SAAS;AACxB,IAAM,sBAAsB,SAAS;AACrC,IAAM,qBAAqB,SAAS;AACpC,IAAM,iBAAiB,SAAS;AAChC,IAAM,gBAAgB,SAAS;", "names": ["t", "options", "copy", "_a"]}